<?php
session_start();
require_once 'config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: admin_login.php');
    exit;
}

// Get dashboard statistics
try {
    // Count total agents
    $stmt = $pdo->query("SELECT COUNT(*) FROM agents");
    $totalAgents = $stmt->fetchColumn();
    
    // Count total clients
    $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
    $totalClients = $stmt->fetchColumn();
    
    // Count total policies
    $stmt = $pdo->query("SELECT COUNT(*) FROM policies");
    $totalPolicies = $stmt->fetchColumn();
    
    // Count pending agents (users without agent assignment)
    $stmt = $pdo->query("SELECT COUNT(*) FROM users u LEFT JOIN agents a ON u.id = a.user_id WHERE a.user_id IS NULL");
    $pendingAgents = $stmt->fetchColumn();
    
    // Get recent activity
    $stmt = $pdo->query("SELECT * FROM activity_logs ORDER BY timestamp DESC LIMIT 10");
    $recentActivity = $stmt->fetchAll();
    
} catch (PDOException $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $totalAgents = $totalClients = $totalPolicies = $pendingAgents = 0;
    $recentActivity = [];
}

$pageTitle = 'ADMIN DASHBOARD';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - CRM System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            text-align: center;
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 16px;
            color: #666;
            font-weight: 500;
        }

        .pending {
            color: #f39c12;
        }

        .actions-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }

        .actions-section h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            display: inline-block;
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            transition: transform 0.2s;
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        .activity-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .activity-section h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .activity-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-action {
            font-weight: 600;
            color: #667eea;
        }

        .activity-time {
            font-size: 12px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1><?php echo $pageTitle; ?></h1>
            <div class="user-info">
                <span>Welcome, <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $totalAgents; ?></div>
                <div class="stat-label">Total Agents</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $totalClients; ?></div>
                <div class="stat-label">Total Clients</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $totalPolicies; ?></div>
                <div class="stat-label">Total Policies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number pending"><?php echo $pendingAgents; ?></div>
                <div class="stat-label">Pending Agent Assignments</div>
            </div>
        </div>

        <div class="actions-section">
            <h2>Quick Actions</h2>
            <div class="action-buttons">
                <a href="admin_agents.php" class="action-btn">Manage Agents</a>
                <a href="admin_assign_agents.php" class="action-btn">Assign Agent IDs</a>
                <a href="clients.php" class="action-btn">View Clients</a>
                <a href="policies.php" class="action-btn">View Policies</a>
                <a href="admin_users.php" class="action-btn">Manage Users</a>
                <a href="admin_reports.php" class="action-btn">Generate Reports</a>
            </div>
        </div>

        <div class="activity-section">
            <h2>Recent Activity</h2>
            <?php if (empty($recentActivity)): ?>
                <p>No recent activity to display.</p>
            <?php else: ?>
                <?php foreach ($recentActivity as $activity): ?>
                    <div class="activity-item">
                        <div class="activity-action"><?php echo htmlspecialchars($activity['action']); ?></div>
                        <div><?php echo htmlspecialchars($activity['details']); ?></div>
                        <div class="activity-time"><?php echo date('M j, Y g:i A', strtotime($activity['timestamp'])); ?></div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
