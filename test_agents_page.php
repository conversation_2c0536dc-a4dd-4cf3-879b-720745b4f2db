<?php
// test_agents_page.php - Test agents functionality

session_start();
require_once 'config.php';

echo "<h1>Test Agents Page Functionality</h1>";

// Simulate admin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';

echo "<h2>Testing Agent Functions</h2>";

// Test 1: getAllAgents function
echo "<h3>1. Testing getAllAgents() function</h3>";
try {
    $agents = getAllAgents();
    echo "✅ getAllAgents() returned " . count($agents) . " agents<br>";
    
    if (count($agents) > 0) {
        echo "<strong>Sample agent data:</strong><br>";
        $firstAgent = $agents[0];
        echo "<pre>" . print_r($firstAgent, true) . "</pre>";
    } else {
        echo "ℹ️ No agents found in database<br>";
    }
} catch (Exception $e) {
    echo "❌ Error in getAllAgents(): " . $e->getMessage() . "<br>";
}

// Test 2: getAllAgentsSorted function
echo "<h3>2. Testing getAllAgentsSorted() function</h3>";
try {
    $sortedAgents = getAllAgentsSorted('name', 'asc');
    echo "✅ getAllAgentsSorted() returned " . count($sortedAgents) . " agents<br>";
    
    if (count($sortedAgents) > 0) {
        echo "<strong>Sorted agents (by name):</strong><br>";
        foreach ($sortedAgents as $agent) {
            $email = $agent['email'] ?? 'No email';
            echo "- {$agent['agent_id']}: {$agent['name']} ({$email})<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error in getAllAgentsSorted(): " . $e->getMessage() . "<br>";
}

// Test 3: Check agent-user relationships
echo "<h3>3. Testing Agent-User Relationships</h3>";
try {
    $stmt = $pdo->query("SELECT a.agent_id, a.name, a.user_id, u.email 
                        FROM agents a 
                        LEFT JOIN users u ON a.user_id = u.id 
                        LIMIT 5");
    $relationships = $stmt->fetchAll();
    
    echo "✅ Found " . count($relationships) . " agent-user relationships:<br>";
    foreach ($relationships as $rel) {
        $userInfo = $rel['user_id'] ? "User ID: {$rel['user_id']}, Email: {$rel['email']}" : "No user linked";
        echo "- Agent {$rel['agent_id']} ({$rel['name']}): $userInfo<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking relationships: " . $e->getMessage() . "<br>";
}

// Test 4: Check for agents without user_id (need admin assignment)
echo "<h3>4. Checking for Unassigned Users</h3>";
try {
    // Users who don't have an agent_id assigned yet
    $stmt = $pdo->query("SELECT u.id, u.email, u.created_at 
                        FROM users u 
                        LEFT JOIN agents a ON u.id = a.user_id 
                        WHERE a.user_id IS NULL");
    $unassignedUsers = $stmt->fetchAll();
    
    if (count($unassignedUsers) > 0) {
        echo "⚠️ Found " . count($unassignedUsers) . " users without agent assignment:<br>";
        foreach ($unassignedUsers as $user) {
            echo "- User ID: {$user['id']}, Email: {$user['email']}, Registered: {$user['created_at']}<br>";
        }
        echo "<br>💡 <strong>These users need admin to assign agent_id</strong><br>";
    } else {
        echo "✅ All users have been assigned to agents<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking unassigned users: " . $e->getMessage() . "<br>";
}

echo "<h2>Testing Agents Page Access</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>✅ Ready to Test</h3>";
echo "<p>The agent functions are working. Now you can:</p>";
echo "<ol>";
echo "<li><strong>Visit agents.php:</strong> <a href='agents.php' target='_blank'>Open agents.php</a></li>";
echo "<li><strong>Check if the page loads</strong> and displays your agents</li>";
echo "<li><strong>Test sorting</strong> by clicking column headers</li>";
echo "<li><strong>Test view/edit/delete</strong> buttons</li>";
echo "</ol>";
echo "</div>";

if (count($agents ?? []) === 0) {
    echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 20px 0;'>";
    echo "<h3>⚠️ No Agents Found</h3>";
    echo "<p>Your agents table is empty. This could mean:</p>";
    echo "<ul>";
    echo "<li>Agents haven't registered yet in FlutterFlow</li>";
    echo "<li>Registered users haven't been assigned agent_ids yet</li>";
    echo "<li>Data import didn't work properly</li>";
    echo "</ul>";
    echo "<p><strong>Next step:</strong> Check your FlutterFlow app or add test data</p>";
    echo "</div>";
}
?>
