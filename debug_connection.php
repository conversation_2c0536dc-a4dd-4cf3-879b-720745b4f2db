<?php
// debug_connection.php - Debug Supabase connection issues

echo "<h1>Supabase Connection Debug</h1>";

// Show current configuration
echo "<h2>Current Configuration</h2>";
$supabase_host = "db.urfzjhcoitebjjqjavdp.supabase.co";
$supabase_port = "5432";
$supabase_dbname = "postgres";
$supabase_username = "postgres";
$supabase_password = "adabdacdaddaeda";

echo "Host: <strong>$supabase_host</strong><br>";
echo "Port: <strong>$supabase_port</strong><br>";
echo "Database: <strong>$supabase_dbname</strong><br>";
echo "Username: <strong>$supabase_username</strong><br>";
echo "Password: <strong>" . str_repeat('*', strlen($supabase_password)) . "</strong><br>";

// Test 1: Basic DNS resolution
echo "<h2>Test 1: DNS Resolution</h2>";
$ip = gethostbyname($supabase_host);
if ($ip === $supabase_host) {
    echo "❌ DNS resolution failed for $supabase_host<br>";
    echo "This means your server cannot resolve the hostname.<br>";
} else {
    echo "✅ DNS resolved $supabase_host to $ip<br>";
}

// Test 2: Try different connection strings
echo "<h2>Test 2: Connection Attempts</h2>";

$connection_attempts = [
    "pgsql:host=$supabase_host;port=$supabase_port;dbname=$supabase_dbname;sslmode=require",
    "pgsql:host=$supabase_host;port=$supabase_port;dbname=$supabase_dbname;sslmode=disable",
    "pgsql:host=$supabase_host;port=$supabase_port;dbname=$supabase_dbname",
];

foreach ($connection_attempts as $index => $dsn) {
    echo "<h3>Attempt " . ($index + 1) . "</h3>";
    echo "DSN: <code>$dsn</code><br>";
    
    try {
        $pdo = new PDO($dsn, $supabase_username, $supabase_password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::ATTR_TIMEOUT => 10
        ]);
        
        echo "✅ Connection successful!<br>";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT version()");
        $version = $stmt->fetchColumn();
        echo "✅ Database version: $version<br>";
        break;
        
    } catch (PDOException $e) {
        echo "❌ Connection failed: " . $e->getMessage() . "<br>";
    }
}

// Test 3: Alternative hosts
echo "<h2>Test 3: Alternative Connection Methods</h2>";

// Sometimes the host format might be different
$alternative_hosts = [
    "db.urfzjhcoitebjjqjavdp.supabase.co",
    "urfzjhcoitebjjqjavdp.supabase.co",
    "aws-0-ap-southeast-1.pooler.supabase.com" // Common pooler format
];

foreach ($alternative_hosts as $host) {
    echo "<h3>Trying host: $host</h3>";
    $dsn = "pgsql:host=$host;port=$supabase_port;dbname=$supabase_dbname;sslmode=disable";
    
    try {
        $pdo = new PDO($dsn, $supabase_username, $supabase_password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        echo "✅ Connection successful with $host!<br>";
        break;
        
    } catch (PDOException $e) {
        echo "❌ Failed with $host: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Network connectivity
echo "<h2>Test 4: Network Connectivity</h2>";

// Test if we can reach the host on port 5432
$connection = @fsockopen($supabase_host, 5432, $errno, $errstr, 10);
if ($connection) {
    echo "✅ Can reach $supabase_host on port 5432<br>";
    fclose($connection);
} else {
    echo "❌ Cannot reach $supabase_host on port 5432<br>";
    echo "Error: $errstr ($errno)<br>";
}

echo "<h2>Recommendations</h2>";
echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800;'>";
echo "<h3>To fix this issue:</h3>";
echo "<ol>";
echo "<li><strong>Check your Supabase project settings:</strong><br>";
echo "   - Go to your Supabase dashboard<br>";
echo "   - Navigate to Settings → Database<br>";
echo "   - Copy the exact connection details</li>";
echo "<li><strong>Verify the host format:</strong><br>";
echo "   - Should be: <code>db.your-project-ref.supabase.co</code><br>";
echo "   - NOT: <code>https://your-project-ref.supabase.co</code></li>";
echo "<li><strong>Check your internet connection</strong></li>";
echo "<li><strong>Try disabling SSL temporarily:</strong> Change <code>sslmode=require</code> to <code>sslmode=disable</code></li>";
echo "</ol>";
echo "</div>";

echo "<h2>Supabase Dashboard Instructions</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-left: 4px solid #2196f3;'>";
echo "<ol>";
echo "<li>Go to <a href='https://supabase.com/dashboard' target='_blank'>Supabase Dashboard</a></li>";
echo "<li>Select your project: <strong>urfzjhcoitebjjqjavdp</strong></li>";
echo "<li>Go to <strong>Settings</strong> → <strong>Database</strong></li>";
echo "<li>Look for <strong>Connection Info</strong> section</li>";
echo "<li>Copy the exact values and update your config.php</li>";
echo "</ol>";
echo "</div>";
?>
