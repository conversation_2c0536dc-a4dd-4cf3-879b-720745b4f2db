<?php
/**
 * Test script to verify agent-related functions work with new schema
 */

require_once 'config.php';

echo "<h1>Testing Agent Functions with New Schema</h1>";

// Test 1: Check if agents table has the correct structure
echo "<h2>1. Checking Agents Table Structure</h2>";
try {
    $stmt = $pdo->query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'agents' ORDER BY ordinal_position");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1'>";
    echo "<tr><th>Column Name</th><th>Data Type</th></tr>";
    foreach ($columns as $column) {
        echo "<tr><td>{$column['column_name']}</td><td>{$column['data_type']}</td></tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

// Test 2: Get all agents
echo "<h2>2. Testing getAllAgents() Function</h2>";
try {
    $agents = getAllAgents();
    echo "✅ Found " . count($agents) . " agents<br>";
    
    if (count($agents) > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Agent Number</th><th>Name</th><th>Email</th></tr>";
        foreach (array_slice($agents, 0, 5) as $agent) {
            echo "<tr>";
            echo "<td>" . ($agent['id'] ?? 'N/A') . "</td>";
            echo "<td>" . ($agent['agent_number'] ?? 'N/A') . "</td>";
            echo "<td>" . ($agent['name'] ?? 'N/A') . "</td>";
            echo "<td>" . ($agent['email'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "❌ Error in getAllAgents(): " . $e->getMessage();
}

// Test 3: Test getAgentById with different identifiers
echo "<h2>3. Testing getAgentById() Function</h2>";
try {
    // Get first agent for testing
    $agents = getAllAgents();
    if (count($agents) > 0) {
        $testAgent = $agents[0];
        $agentId = $testAgent['id'];
        $agentNumber = $testAgent['agent_number'];
        
        echo "<h3>Testing with ID: $agentId</h3>";
        $agent1 = getAgentById($agentId);
        if ($agent1) {
            echo "✅ Found agent by ID: " . $agent1['name'] . "<br>";
        } else {
            echo "❌ Could not find agent by ID<br>";
        }
        
        if ($agentNumber) {
            echo "<h3>Testing with Agent Number: $agentNumber</h3>";
            $agent2 = getAgentById($agentNumber);
            if ($agent2) {
                echo "✅ Found agent by number: " . $agent2['name'] . "<br>";
            } else {
                echo "❌ Could not find agent by number<br>";
            }
        }
    } else {
        echo "⚠️ No agents found to test with<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error in getAgentById(): " . $e->getMessage();
}

// Test 4: Test getAgentEducation function
echo "<h2>4. Testing getAgentEducation() Function</h2>";
try {
    $agents = getAllAgents();
    if (count($agents) > 0) {
        $testAgent = $agents[0];
        $agentId = $testAgent['id'];
        
        echo "Testing with agent ID: $agentId<br>";
        $education = getAgentEducation($agentId);
        echo "✅ getAgentEducation() executed successfully<br>";
        echo "Found " . count($education) . " education records<br>";
        
        if (count($education) > 0) {
            echo "<table border='1'>";
            echo "<tr><th>Institution</th><th>Qualification</th><th>Year</th></tr>";
            foreach ($education as $edu) {
                echo "<tr>";
                echo "<td>" . ($edu['institution'] ?? 'N/A') . "</td>";
                echo "<td>" . ($edu['qualification'] ?? 'N/A') . "</td>";
                echo "<td>" . ($edu['year_completed'] ?? 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "⚠️ No agents found to test with<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error in getAgentEducation(): " . $e->getMessage() . "<br>";
    echo "This was the original error you encountered!<br>";
}

// Test 5: Test getAgentDocuments function
echo "<h2>5. Testing getAgentDocuments() Function</h2>";
try {
    $agents = getAllAgents();
    if (count($agents) > 0) {
        $testAgent = $agents[0];
        $agentId = $testAgent['id'];
        
        echo "Testing with agent ID: $agentId<br>";
        $documents = getAgentDocuments($agentId);
        echo "✅ getAgentDocuments() executed successfully<br>";
        echo "Found " . count($documents) . " document records<br>";
        
    } else {
        echo "⚠️ No agents found to test with<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error in getAgentDocuments(): " . $e->getMessage();
}

// Test 6: Check foreign key relationships
echo "<h2>6. Testing Foreign Key Relationships</h2>";
try {
    echo "<h3>Policies -> Agents</h3>";
    $stmt = $pdo->query("SELECT p.policy_id, p.agent_id, a.name as agent_name 
                        FROM policies p 
                        LEFT JOIN agents a ON p.agent_id = a.id 
                        LIMIT 5");
    $policies = $stmt->fetchAll();
    
    echo "✅ Found " . count($policies) . " policies with agent relationships<br>";
    if (count($policies) > 0) {
        echo "<table border='1'>";
        echo "<tr><th>Policy ID</th><th>Agent ID</th><th>Agent Name</th></tr>";
        foreach ($policies as $policy) {
            echo "<tr>";
            echo "<td>" . $policy['policy_id'] . "</td>";
            echo "<td>" . $policy['agent_id'] . "</td>";
            echo "<td>" . ($policy['agent_name'] ?? 'No agent assigned') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing foreign keys: " . $e->getMessage();
}

echo "<h2>✅ All Tests Complete!</h2>";
echo "<p>If you see this message without errors above, your schema update was successful!</p>";
?>
