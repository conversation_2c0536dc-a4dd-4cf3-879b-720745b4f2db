<?php
// check_php_extensions.php - Check PHP extensions and configuration

echo "<h1>PHP Extensions Check</h1>";

echo "<h2>PHP Version</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "PHP SAPI: " . php_sapi_name() . "<br>";

echo "<h2>PDO Drivers Available</h2>";
$drivers = PDO::getAvailableDrivers();
echo "Available PDO drivers: " . implode(', ', $drivers) . "<br>";

if (in_array('pgsql', $drivers)) {
    echo "✅ PostgreSQL PDO driver (pdo_pgsql) is available<br>";
} else {
    echo "❌ PostgreSQL PDO driver (pdo_pgsql) is NOT available<br>";
}

echo "<h2>All Loaded Extensions</h2>";
$extensions = get_loaded_extensions();
sort($extensions);

$postgresql_extensions = array_filter($extensions, function($ext) {
    return stripos($ext, 'pgsql') !== false || stripos($ext, 'postgres') !== false;
});

if (!empty($postgresql_extensions)) {
    echo "✅ PostgreSQL extensions found: " . implode(', ', $postgresql_extensions) . "<br>";
} else {
    echo "❌ No PostgreSQL extensions found<br>";
}

echo "<h3>All Extensions:</h3>";
echo "<div style='columns: 3; column-gap: 20px;'>";
foreach ($extensions as $ext) {
    $color = (stripos($ext, 'pgsql') !== false || stripos($ext, 'postgres') !== false) ? 'green' : 'black';
    echo "<span style='color: $color;'>$ext</span><br>";
}
echo "</div>";

echo "<h2>PHP Configuration</h2>";
echo "Configuration file (php.ini) path: " . php_ini_loaded_file() . "<br>";
echo "Additional .ini files parsed: " . php_ini_scanned_files() . "<br>";

echo "<h2>Extension Directory</h2>";
echo "Extension directory: " . ini_get('extension_dir') . "<br>";

// Check if pdo_pgsql.dll exists in extension directory
$ext_dir = ini_get('extension_dir');
$pgsql_dll = $ext_dir . DIRECTORY_SEPARATOR . 'pdo_pgsql.dll';
$pgsql_so = $ext_dir . DIRECTORY_SEPARATOR . 'pdo_pgsql.so';

echo "<h2>Extension Files Check</h2>";
if (file_exists($pgsql_dll)) {
    echo "✅ pdo_pgsql.dll found at: $pgsql_dll<br>";
} elseif (file_exists($pgsql_so)) {
    echo "✅ pdo_pgsql.so found at: $pgsql_so<br>";
} else {
    echo "❌ pdo_pgsql extension file not found in extension directory<br>";
    echo "Checked for: $pgsql_dll<br>";
    echo "Checked for: $pgsql_so<br>";
}

echo "<h2>Recommendations</h2>";
if (!in_array('pgsql', PDO::getAvailableDrivers())) {
    echo "<div style='background: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>";
    echo "<h3>To Fix PostgreSQL Connection:</h3>";
    echo "<ol>";
    echo "<li><strong>Edit php.ini file:</strong><br>";
    echo "   - Open XAMPP Control Panel<br>";
    echo "   - Click 'Config' next to Apache → PHP (php.ini)<br>";
    echo "   - Find these lines and remove the semicolon (;):<br>";
    echo "   <code>;extension=pdo_pgsql</code> → <code>extension=pdo_pgsql</code><br>";
    echo "   <code>;extension=pgsql</code> → <code>extension=pgsql</code></li>";
    echo "<li><strong>Restart Apache</strong> in XAMPP Control Panel</li>";
    echo "<li><strong>Refresh this page</strong> to verify the fix</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50;'>";
    echo "✅ PostgreSQL extensions are properly configured!<br>";
    echo "You can now test your Supabase connection.";
    echo "</div>";
}

echo "<h2>Alternative Solutions</h2>";
echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800;'>";
echo "<h3>If editing php.ini doesn't work:</h3>";
echo "<ol>";
echo "<li><strong>Download newer XAMPP:</strong> Some older XAMPP versions don't include PostgreSQL extensions</li>";
echo "<li><strong>Use WAMP instead:</strong> WAMP typically includes PostgreSQL extensions by default</li>";
echo "<li><strong>Install standalone PHP:</strong> Download PHP with PostgreSQL support from php.net</li>";
echo "<li><strong>Use Docker:</strong> Run PHP with PostgreSQL in a Docker container</li>";
echo "</ol>";
echo "</div>";
?>
