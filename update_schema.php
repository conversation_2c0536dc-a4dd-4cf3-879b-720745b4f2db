<?php
// update_schema.php - This file will update the database schema to support alphanumeric agent IDs

// Database connection parameters (same as config.php)
require_once 'config.php';

// Start output buffer to capture any errors
ob_start();

try {
    // Update agents table
    $pdo->exec("ALTER TABLE agents MODIFY COLUMN agent_id VARCHAR(20)");
    echo "✓ Updated agents table schema<br>";
    
    // Update agent_client table
    $pdo->exec("ALTER TABLE agent_client MODIFY COLUMN agent_id VARCHAR(20)");
    echo "✓ Updated agent_client table schema<br>";
    
    // Update educationdetails table
    $pdo->exec("ALTER TABLE educationdetails MODIFY COLUMN agent_id VARCHAR(20)");
    echo "✓ Updated educationdetails table schema<br>";
    
    // Update agent_documents table
    $pdo->exec("ALTER TABLE agent_documents MODIFY COLUMN agent_id VARCHAR(20)");
    echo "✓ Updated agent_documents table schema<br>";
    
    echo "<p style='color:green;font-weight:bold;'>Schema update completed successfully!</p>";
    echo "<p>Your database now supports alphanumeric agent IDs.</p>";
    
} catch (PDOException $e) {
    echo "<p style='color:red;font-weight:bold;'>Error updating schema: " . $e->getMessage() . "</p>";
    // Output the SQL that failed
    echo "<p>Last SQL query: " . $pdo->errorInfo()[2] . "</p>";
}

$output = ob_get_clean();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Schema Update</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        a {
            display: inline-block;
            margin-top: 20px;
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 4px;
        }
        a:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Schema Update</h1>
        <?php echo $output; ?>
        <a href="agents.php">Back to Agents List</a>
    </div>
</body>
</html> 