# Database Schema Update Summary

## Overview
Your PHP CRM system has been updated to match the new database schema from `database.sql`. This document outlines all the changes made and how to proceed.

## Key Schema Changes

### 1. Agents Table Structure
**Before:**
- Primary key: `agent_id VARCHAR(50)`
- No separate agent number field

**After:**
- Primary key: `id INTEGER` (auto-increment)
- Separate field: `agent_number VARCHAR(50)` (unique)
- All foreign key references now use the integer `id`

### 2. Clients Table
**Added new fields:**
- `nric VARCHAR(50)` - National Registration Identity Card
- `signature VARCHAR(255)` - Signature file path
- `bankcard VARCHAR(255)` - Bank card information

### 3. Admins Table (New)
**New table for admin authentication:**
- `id INTEGER` (primary key)
- `username VA<PERSON><PERSON><PERSON>(255)` (unique)
- `password VARCHAR(255)` (hashed)
- `created_at TIMESTAMP`

### 4. Beneficiaries Table
**Updated structure:**
- Primary key changed from `id` to `beneficiary_id`
- Added `policy_id` foreign key to policies table
- Added fields: `ic_number`, `date_of_birth`, `phone_number`, `email`, `address`, `percentage`
- Removed `agent_id` and `phone` fields

### 5. Foreign Key Updates
- `policies.agent_id` now references `agents.id` (integer)
- `educationdetails.agent_id` now references `agents.id` (integer)
- `beneficiaries.policy_id` now references `policies.policy_id`

## Files Updated

### Schema Files
- ✅ `setup_flutterflow_schema.php` - Updated to new schema
- ✅ `setup_database_schema.php` - Updated to new schema
- ✅ `migrate_to_new_schema.php` - **NEW** Migration script

### Authentication Files
- ✅ `admin_login.php` - **NEW** Admin login page
- ✅ `admin_dashboard.php` - **NEW** Admin dashboard
- ✅ `logout.php` - Updated to handle both admin and agent logout

### Application Files
- ✅ `config.php` - Updated agent-related functions
- ✅ `policies.php` - Updated foreign key references
- ✅ `client_add.php` - Added support for new client fields

## Migration Steps

### Step 1: Run Migration Script
```
http://your-domain/migrate_to_new_schema.php
```

This script will:
- Create the admins table
- Add new columns to existing tables
- Update foreign key relationships
- Migrate data from old to new structure
- Create a default admin user

### Step 2: Test Your Application
1. Test agent login functionality
2. Test admin login (username: `admin`, password: `admin123`)
3. Verify client management works
4. Check policy management
5. Test agent assignment functionality

### Step 3: Update Admin Password
**Important:** Change the default admin password immediately:
1. Login as admin
2. Navigate to admin settings
3. Change password from `admin123` to something secure

## New Admin Features

### Admin Login
- Separate login page: `admin_login.php`
- Username/password authentication
- Session management with user type tracking

### Admin Dashboard
- Overview statistics
- Quick action buttons
- Recent activity log
- Agent management tools

## Backward Compatibility

### Agent ID Handling
The system now handles both old and new agent ID formats:
- Functions accept both integer IDs and agent numbers
- Automatic lookup in both `id` and `agent_number` fields
- Gradual migration support

### Database Queries
All database queries have been updated to use:
- `agents.id` for foreign key relationships
- `agents.agent_number` for display and search
- Proper joins with the new structure

## Troubleshooting

### Common Issues

1. **Foreign Key Errors**
   - Run the migration script to update relationships
   - Check that all agent references use integer IDs

2. **Login Issues**
   - Verify admin table was created
   - Check default admin user exists
   - Ensure session handling works for both user types

3. **Missing Data**
   - Check migration script completed successfully
   - Verify data was copied from old to new columns
   - Review error logs for any failed operations

### Verification Queries

```sql
-- Check agents table structure
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'agents' ORDER BY ordinal_position;

-- Verify foreign key relationships
SELECT * FROM policies p 
JOIN agents a ON p.agent_id = a.id 
LIMIT 5;

-- Check admin table
SELECT id, username, created_at FROM admins;
```

## Next Steps

1. **Test thoroughly** - Verify all functionality works
2. **Update passwords** - Change default admin credentials
3. **Train users** - Inform admins about new login process
4. **Monitor logs** - Watch for any errors during transition
5. **Backup regularly** - Ensure data safety during transition period

## Support

If you encounter any issues:
1. Check the migration script output for errors
2. Review database logs for constraint violations
3. Verify all foreign key relationships are correct
4. Test with a small dataset first

The system is now fully compatible with your new database schema while maintaining backward compatibility where possible.
