<?php
// test_supabase_connection.php - Test Supabase PostgreSQL connection

echo "<h1>Supabase PostgreSQL Connection Test</h1>";

// Test 1: Basic connection
echo "<h2>Test 1: Basic Connection</h2>";
try {
    // TODO: Update these with your actual Supabase credentials
    $supabase_host = "db.your-project-ref.supabase.co";  // Replace with your Supabase host
    $supabase_port = "5432";
    $supabase_dbname = "postgres";  // Default Supabase database name
    $supabase_username = "postgres";  // Default Supabase username
    $supabase_password = "your-database-password";  // Replace with your Supabase password

    $dsn = "pgsql:host=$supabase_host;port=$supabase_port;dbname=$supabase_dbname;sslmode=require";
    $pdo = new PDO($dsn, $supabase_username, $supabase_password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    echo "✅ Connection successful!<br>";
    
    // Test timezone setting
    $pdo->exec("SET timezone = 'UTC'");
    echo "✅ Timezone set to UTC<br>";
    
} catch (PDOException $e) {
    echo "❌ Connection failed: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Check if tables exist
echo "<h2>Test 2: Check Tables</h2>";
try {
    $tables = ['agents', 'clients', 'policies', 'educationdetails', 'documents', 'users', 'activity_logs'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = ?
        )");
        $stmt->execute([$table]);
        $exists = $stmt->fetchColumn();
        
        if ($exists) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "❌ Table '$table' does not exist<br>";
        }
    }
} catch (PDOException $e) {
    echo "❌ Error checking tables: " . $e->getMessage() . "<br>";
}

// Test 3: Test basic CRUD operations
echo "<h2>Test 3: Basic CRUD Operations</h2>";

// Test INSERT
try {
    echo "<h3>Testing INSERT operation</h3>";
    $stmt = $pdo->prepare("INSERT INTO agents (agent_id, name, ic_number, gender, date_of_birth, phone_number, address, created_at, updated_at, status) 
                          VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)");
    $stmt->execute(['TEST001', 'Test Agent', '123456789', 'Male', '1990-01-01', '0123456789', 'Test Address', 'Pending']);
    echo "✅ INSERT operation successful<br>";
} catch (PDOException $e) {
    echo "❌ INSERT failed: " . $e->getMessage() . "<br>";
}

// Test SELECT
try {
    echo "<h3>Testing SELECT operation</h3>";
    $stmt = $pdo->prepare("SELECT * FROM agents WHERE agent_id = ?");
    $stmt->execute(['TEST001']);
    $agent = $stmt->fetch();
    
    if ($agent) {
        echo "✅ SELECT operation successful. Found agent: " . $agent['name'] . "<br>";
    } else {
        echo "❌ SELECT operation failed - no data found<br>";
    }
} catch (PDOException $e) {
    echo "❌ SELECT failed: " . $e->getMessage() . "<br>";
}

// Test UPDATE
try {
    echo "<h3>Testing UPDATE operation</h3>";
    $stmt = $pdo->prepare("UPDATE agents SET name = ?, updated_at = NOW() WHERE agent_id = ?");
    $stmt->execute(['Test Agent Updated', 'TEST001']);
    echo "✅ UPDATE operation successful<br>";
} catch (PDOException $e) {
    echo "❌ UPDATE failed: " . $e->getMessage() . "<br>";
}

// Test DELETE
try {
    echo "<h3>Testing DELETE operation</h3>";
    $stmt = $pdo->prepare("DELETE FROM agents WHERE agent_id = ?");
    $stmt->execute(['TEST001']);
    echo "✅ DELETE operation successful<br>";
} catch (PDOException $e) {
    echo "❌ DELETE failed: " . $e->getMessage() . "<br>";
}

// Test 4: Test config.php functions
echo "<h2>Test 4: Testing config.php functions</h2>";
try {
    require_once 'config.php';
    
    // Test getAllAgents function
    $agents = getAllAgents();
    echo "✅ getAllAgents() function works. Found " . count($agents) . " agents<br>";
    
    // Test getAllClients function
    $clients = getAllClients();
    echo "✅ getAllClients() function works. Found " . count($clients) . " clients<br>";
    
    // Test agentIdExists function
    $exists = agentIdExists('AG001');
    echo "✅ agentIdExists() function works. AG001 " . ($exists ? "exists" : "does not exist") . "<br>";
    
} catch (Exception $e) {
    echo "❌ Config function test failed: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Complete!</h2>";
echo "<p>If you see mostly green checkmarks (✅), your Supabase PostgreSQL migration is working correctly!</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li>1. Update the Supabase credentials in config.php with your actual values</li>";
echo "<li>2. Import your data from the SQL dump files</li>";
echo "<li>3. Test your application pages</li>";
echo "</ul>";
?>
