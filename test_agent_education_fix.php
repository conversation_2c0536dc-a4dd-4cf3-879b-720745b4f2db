<?php
/**
 * Test the specific getAgentEducation function that was causing the error
 */

require_once 'config.php';

echo "<h1>Testing Agent Education Function Fix</h1>";

try {
    echo "<h2>1. Testing Database Connection</h2>";
    $stmt = $pdo->query("SELECT 1");
    echo "✅ Database connection successful<br>";
    
    echo "<h2>2. Checking Agents Table Structure</h2>";
    $stmt = $pdo->query("SELECT column_name FROM information_schema.columns WHERE table_name = 'agents' ORDER BY ordinal_position");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Agents table columns: " . implode(', ', $columns) . "<br>";
    
    $hasId = in_array('id', $columns);
    $hasOldAgentId = in_array('agent_id', $columns);
    
    echo "Has 'id' column: " . ($hasId ? "✅ Yes" : "❌ No") . "<br>";
    echo "Has old 'agent_id' column: " . ($hasOldAgentId ? "⚠️ Yes (needs migration)" : "✅ No") . "<br>";
    
    echo "<h2>3. Testing getAgentEducation Function</h2>";
    
    // Get first agent
    $stmt = $pdo->query("SELECT id FROM agents LIMIT 1");
    $agent = $stmt->fetch();
    
    if ($agent) {
        $agentId = $agent['id'];
        echo "Testing with agent ID: $agentId<br>";
        
        // This was the failing function call
        $education = getAgentEducation($agentId);
        
        echo "✅ SUCCESS! getAgentEducation($agentId) executed without errors<br>";
        echo "Found " . count($education) . " education records<br>";
        
        if (count($education) > 0) {
            echo "<h3>Sample Education Records:</h3>";
            foreach (array_slice($education, 0, 3) as $edu) {
                echo "- Institution: " . ($edu['institution'] ?? 'N/A') . 
                     ", Qualification: " . ($edu['qualification'] ?? 'N/A') . 
                     ", Year: " . ($edu['year_completed'] ?? 'N/A') . "<br>";
            }
        }
        
    } else {
        echo "⚠️ No agents found in database<br>";
        
        // Let's try to create a test agent
        echo "<h3>Creating Test Agent</h3>";
        $testData = [
            'name' => 'Test Agent',
            'ic_number' => '123456789012',
            'gender' => 'Male',
            'date_of_birth' => '1990-01-01',
            'phone_number' => '0123456789',
            'address' => 'Test Address',
            'beneficiary_phone' => '0123456789',
            'work_experience' => 'Test Experience',
            'user_id' => null,
            'status' => 'Active'
        ];
        
        $result = createAgent($testData);
        if ($result) {
            echo "✅ Test agent created successfully<br>";
            
            // Get the new agent and test again
            $stmt = $pdo->query("SELECT id FROM agents ORDER BY id DESC LIMIT 1");
            $newAgent = $stmt->fetch();
            
            if ($newAgent) {
                $newAgentId = $newAgent['id'];
                echo "Testing with new agent ID: $newAgentId<br>";
                
                $education = getAgentEducation($newAgentId);
                echo "✅ SUCCESS! getAgentEducation($newAgentId) executed without errors<br>";
                echo "Found " . count($education) . " education records<br>";
            }
        } else {
            echo "❌ Failed to create test agent<br>";
        }
    }
    
    echo "<h2>4. Testing Other Agent Functions</h2>";
    
    // Test getAllAgents
    $agents = getAllAgents();
    echo "getAllAgents(): Found " . count($agents) . " agents ✅<br>";
    
    if (count($agents) > 0) {
        $testAgent = $agents[0];
        $testId = $testAgent['id'];
        
        // Test getAgentById
        $agent = getAgentById($testId);
        echo "getAgentById($testId): " . ($agent ? "✅ Found agent: " . $agent['name'] : "❌ Not found") . "<br>";
        
        // Test getAgentDocuments
        $documents = getAgentDocuments($testId);
        echo "getAgentDocuments($testId): Found " . count($documents) . " documents ✅<br>";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
}

echo "<h2>✅ Test Complete</h2>";
echo "<p>If you see this without errors, the fix was successful!</p>";
echo "<p><strong>You can now try accessing agent_view.php again.</strong></p>";
?>
