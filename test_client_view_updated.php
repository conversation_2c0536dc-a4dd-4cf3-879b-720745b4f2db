<?php
// test_client_view_updated.php - Test updated client view functionality

session_start();
require_once 'config.php';

echo "<h1>Test Updated Client View Functionality</h1>";

// Simulate admin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';

echo "<h2>Testing Updated Client View</h2>";

// Test 1: Check available clients
echo "<h3>1. Available Clients for Testing</h3>";
try {
    $clients = getAllClients();
    echo "✅ Found " . count($clients) . " clients<br>";
    
    if (count($clients) > 0) {
        echo "<strong>Available clients for testing:</strong><br>";
        foreach (array_slice($clients, 0, 3) as $client) {
            $clientId = $client['client_id'];
            $clientName = $client['name'] ?? 'No name';
            echo "- <a href='client_view.php?id=$clientId' target='_blank'>$clientId: $clientName</a><br>";
        }
    } else {
        echo "⚠️ No clients found. You need to add some clients first.<br>";
    }
} catch (Exception $e) {
    echo "❌ Error getting clients: " . $e->getMessage() . "<br>";
}

// Test 2: Test policy display for clients with multiple policies
echo "<h3>2. Testing Policy Display</h3>";
try {
    $stmt = $pdo->query("SELECT 
        c.client_id, 
        c.name as client_name,
        COUNT(p.policy_id) as policy_count
        FROM clients c
        LEFT JOIN policies p ON c.client_id = p.client_id
        GROUP BY c.client_id, c.name
        HAVING COUNT(p.policy_id) > 0
        ORDER BY COUNT(p.policy_id) DESC
        LIMIT 5");
    
    $clientsWithPolicies = $stmt->fetchAll();
    
    echo "✅ Found " . count($clientsWithPolicies) . " clients with policies:<br>";
    foreach ($clientsWithPolicies as $client) {
        echo "- <a href='client_view.php?id={$client['client_id']}' target='_blank'>";
        echo "{$client['client_name']} ({$client['policy_count']} policies)</a><br>";
    }
    
    if (count($clientsWithPolicies) === 0) {
        echo "ℹ️ No clients with policies found<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking policies: " . $e->getMessage() . "<br>";
}

// Test 3: Test getClientPolicies function with detailed policy info
echo "<h3>3. Testing Policy Details Display</h3>";
try {
    $clients = getAllClients();
    if (count($clients) > 0) {
        $firstClient = $clients[0];
        $testId = $firstClient['client_id'];
        
        $policies = getClientPolicies($testId);
        echo "✅ Client {$testId} has " . count($policies) . " policies<br>";
        
        if (count($policies) > 0) {
            echo "<strong>Policy details that should be displayed:</strong><br>";
            foreach ($policies as $policy) {
                echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
                echo "<strong>Policy ID:</strong> {$policy['policy_id']}<br>";
                echo "<strong>Plan Type:</strong> " . ($policy['plan_type'] ?? 'N/A') . "<br>";
                echo "<strong>Sum Covered:</strong> " . ($policy['sum_covered'] ?? 'N/A') . "<br>";
                echo "<strong>Status:</strong> " . ($policy['status'] ?? 'N/A') . "<br>";
                echo "<strong>Agent:</strong> " . ($policy['agent_name'] ?? 'No agent assigned') . "<br>";
                echo "<strong>Start Date:</strong> " . ($policy['start_date'] ?? 'N/A') . "<br>";
                echo "<strong>End Date:</strong> " . ($policy['end_date'] ?? 'N/A') . "<br>";
                echo "</div>";
            }
        } else {
            echo "ℹ️ No policies found for this client<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error testing policy details: " . $e->getMessage() . "<br>";
}

// Test 4: Check for clients with multiple policies
echo "<h3>4. Testing Multiple Policies Display</h3>";
try {
    $stmt = $pdo->query("SELECT 
        c.client_id, 
        c.name,
        COUNT(p.policy_id) as policy_count,
        STRING_AGG(p.policy_id, ', ') as policy_ids
        FROM clients c
        JOIN policies p ON c.client_id = p.client_id
        GROUP BY c.client_id, c.name
        HAVING COUNT(p.policy_id) > 1
        LIMIT 3");
    
    $multiPolicyClients = $stmt->fetchAll();
    
    if (count($multiPolicyClients) > 0) {
        echo "✅ Found " . count($multiPolicyClients) . " clients with multiple policies:<br>";
        foreach ($multiPolicyClients as $client) {
            echo "- <strong>{$client['name']}</strong> ({$client['policy_count']} policies): {$client['policy_ids']}<br>";
            echo "  <a href='client_view.php?id={$client['client_id']}' target='_blank'>→ Test multiple policy display</a><br>";
        }
    } else {
        echo "ℹ️ No clients with multiple policies found<br>";
        echo "💡 You can test by adding multiple policies to a client in your FlutterFlow app<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking multiple policies: " . $e->getMessage() . "<br>";
}

echo "<h2>Summary of Changes Made</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>✅ Policy Section Updates</h3>";
echo "<ul>";
echo "<li><strong>Removed:</strong> 'Add New Policy' button</li>";
echo "<li><strong>Removed:</strong> Policy action buttons (View Details, Edit, Delete)</li>";
echo "<li><strong>Removed:</strong> Policy delete JavaScript functions</li>";
echo "<li><strong>Removed:</strong> Unused CSS for buttons and actions</li>";
echo "<li><strong>Kept:</strong> Full policy details display</li>";
echo "<li><strong>Supports:</strong> Multiple policies per client</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 15px; border-left: 4px solid #2196f3; margin: 20px 0;'>";
echo "<h3>📋 What Client View Now Shows</h3>";
echo "<ul>";
echo "<li><strong>Client Information:</strong> All personal and contact details</li>";
echo "<li><strong>Policy Information:</strong> Read-only display of all policies</li>";
echo "<li><strong>Policy Details:</strong> Plan type, sum covered, status, agent, dates</li>";
echo "<li><strong>Multiple Policies:</strong> All policies displayed in separate cards</li>";
echo "<li><strong>No Edit/Delete:</strong> Pure view-only interface</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 20px 0;'>";
echo "<h3>🧪 Test the Updated Client View</h3>";
echo "<p>Click the links above to test the updated client_view.php page. You should see:</p>";
echo "<ol>";
echo "<li><strong>No 'Add New Policy' button</strong> in the policy section</li>";
echo "<li><strong>No action buttons</strong> on individual policies</li>";
echo "<li><strong>All policy details displayed</strong> directly in cards</li>";
echo "<li><strong>Multiple policies</strong> shown as separate cards if client has them</li>";
echo "<li><strong>Clean, read-only interface</strong> for viewing client and policy information</li>";
echo "</ol>";
echo "</div>";
?>
