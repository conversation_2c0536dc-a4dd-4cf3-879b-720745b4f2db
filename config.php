<?php
// config.php - Supabase PostgreSQL Database connection and helper functions

// Supabase Configuration
// TODO: Replace these with your actual Supabase credentials
$supabase_host = "db.urfzjhcoitebjjqjavdp.supabase.co";  // Replace with your Supabase host
$supabase_port = "5432";
$supabase_dbname = "postgres";  // Default Supabase database name
$supabase_username = "postgres";  // Default Supabase username
$supabase_password = "adabdacdaddaeda";  // Replace with your Supabase password

// Create PostgreSQL connection using PDO
try {
    $dsn = "pgsql:host=$supabase_host;port=$supabase_port;dbname=$supabase_dbname;sslmode=require";
    $pdo = new PDO($dsn, $supabase_username, $supabase_password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);

    // Set timezone to UTC (Supabase default)
    $pdo->exec("SET timezone = 'UTC'");

} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Legacy mysqli connection variable for backward compatibility
// Note: This will be null since we're using PostgreSQL
$conn = null;

// Function to log admin activity (FlutterFlow compatible)
function logActivity($userId, $action, $details) {
    global $pdo;
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    // Check if activity_logs table exists, if not, skip logging
    try {
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, timestamp) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([$userId, $action, $details, $ip]);
    } catch (PDOException $e) {
        // Silently fail if table doesn't exist
        error_log("Activity logging failed: " . $e->getMessage());
    }
}

// Authentication functions (Admin login)
function authenticateUser($username, $password) {
    global $pdo;
    // Check against admins table for PHP admin login
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ?");
    $stmt->execute([$username]);
    $admin = $stmt->fetch();

    if ($admin && password_verify($password, $admin['password'])) {
        return $admin;
    }
    return false;
}

// Agent functions
function getAllAgents() {
    global $pdo;
    $agents = array();

    // Join with users table to get email
    $sql = "SELECT a.*, u.email
            FROM agents a
            LEFT JOIN users u ON a.user_id = u.id
            ORDER BY a.name ASC";
    $result = $pdo->query($sql);

    if ($result && $result->rowCount() > 0) {
        while ($row = $result->fetch()) {
            $agents[] = $row;
        }
    }

    return $agents;
}

function getAgentById($identifier) {
    global $pdo;
    // Join with users table to get email, and handle both agent_number and id lookups
    // First try as numeric ID, then as agent_number
    if (is_numeric($identifier)) {
        $stmt = $pdo->prepare("SELECT a.*, u.email
                              FROM agents a
                              LEFT JOIN users u ON a.user_id = u.id
                              WHERE a.id = ?");
        $stmt->execute([$identifier]);
    } else {
        $stmt = $pdo->prepare("SELECT a.*, u.email
                              FROM agents a
                              LEFT JOIN users u ON a.user_id = u.id
                              WHERE a.agent_number = ?");
        $stmt->execute([$identifier]);
    }
    return $stmt->fetch();
}

function createAgent($data) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO agents (agent_number, name, ic_number, gender, date_of_birth, phone_number, address,
                          beneficiary_phone, work_experience, user_id, status, created_at, updated_at)
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
    $stmt->execute([
        $data['agent_number'] ?? null,
        $data['name'],
        $data['ic_number'],
        $data['gender'],
        $data['date_of_birth'],
        $data['phone_number'],
        $data['address'],
        $data['beneficiary_phone'] ?? null,
        $data['work_experience'] ?? null,
        $data['user_id'] ?? null,
        $data['status'] ?? null
    ]);

    return $pdo->lastInsertId(); // Return the auto-generated id
}

/**
 * Update agent details in the database
 * 
 * @param string $oldAgentId Original agent ID
 * @param array $data Agent data to update
 * @return bool Success status
 */
function updateAgent($oldAgentId, $data) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Check if the agent ID has changed
        $newAgentId = $data['agent_id'];
        $idChanged = ($oldAgentId !== $newAgentId);
        
        // Build the update query
        $sql = "UPDATE agents SET
                name = :name,
                ic_number = :ic_number,
                gender = :gender,
                date_of_birth = :date_of_birth,
                phone_number = :phone_number,
                address = :address,
                beneficiary_phone = :beneficiary_phone,
                work_experience = :work_experience,
                updated_at = NOW()";

        // Add photo to update if it's provided
        if (isset($data['photo'])) {
            $sql .= ", photo = :photo";
        }

        // If ID changed, update that too
        if ($idChanged) {
            $sql .= ", agent_id = :new_agent_id";
        }

        $sql .= " WHERE agent_id = :old_agent_id";
        
        $stmt = $pdo->prepare($sql);
        
        // Bind parameters
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':ic_number', $data['ic_number']);
        $stmt->bindParam(':gender', $data['gender']);
        $stmt->bindParam(':date_of_birth', $data['date_of_birth']);
        $stmt->bindParam(':phone_number', $data['phone_number']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':beneficiary_phone', $data['beneficiary_phone']);
        $stmt->bindParam(':work_experience', $data['work_experience']);
        $stmt->bindParam(':old_agent_id', $oldAgentId);
        
        if (isset($data['photo'])) {
            $stmt->bindParam(':photo', $data['photo']);
        }
        
        if ($idChanged) {
            $stmt->bindParam(':new_agent_id', $newAgentId);
        }
        
        $result = $stmt->execute();
        
        if (!$result) {
            $errorInfo = $stmt->errorInfo();
            error_log("SQL execution failed: " . print_r($errorInfo, true));
            throw new Exception("SQL execution failed: " . $errorInfo[2]);
        }
        
        $rowCount = $stmt->rowCount();
        
        // If the agent ID changed, update related tables
        if ($idChanged && $result) {
            // Update policies table
            $stmt = $pdo->prepare("UPDATE policies SET agent_id = :new_agent_id WHERE agent_id = :old_agent_id");
            $stmt->bindParam(':new_agent_id', $newAgentId);
            $stmt->bindParam(':old_agent_id', $oldAgentId);
            $stmt->execute();

            // Log the ID change
            if (isset($_SESSION['user_id'])) {
                logActivity($_SESSION['user_id'], "update_agent", "Updated agent. ID changed from {$oldAgentId} to {$newAgentId}");
            }
        } else if ($result) {
            if (isset($_SESSION['user_id'])) {
                logActivity($_SESSION['user_id'], "update_agent", "Updated agent {$oldAgentId}");
            }
        }
        
        $pdo->commit();
        return true; // Return true if SQL executed successfully, regardless of row count
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Database error in updateAgent: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("General error in updateAgent: " . $e->getMessage());
        return false;
    }
}

function deleteAgent($identifier) {
    global $pdo;

    try {
        // Start transaction
        $pdo->beginTransaction();

        // First get the agent to find both id and agent_id
        $agent = getAgentById($identifier);
        if (!$agent) {
            throw new Exception("Agent not found");
        }

        $agentId = $agent['agent_id'];
        $id = $agent['id'];

        // 1. Delete from policies (uses agent_id)
        if ($agentId) {
            $stmt = $pdo->prepare("DELETE FROM policies WHERE agent_id = ?");
            $stmt->execute([$agentId]);
        }

        // 2. Delete from educationdetails (uses agent_id)
        if ($agentId) {
            $stmt = $pdo->prepare("DELETE FROM educationdetails WHERE agent_id = ?");
            $stmt->execute([$agentId]);
        }

        // 3. Finally delete from agents table (uses id)
        $stmt = $pdo->prepare("DELETE FROM agents WHERE id = ?");
        $stmt->execute([$id]);

        // Commit transaction
        $pdo->commit();

        return true;
    } catch (Exception $e) {
        // Rollback on error
        $pdo->rollBack();
        error_log("Error deleting agent: " . $e->getMessage());
        return false;
    }
}

// Client functions (PostgreSQL version)
function getAllClients() {
    global $pdo;
    $clients = [];

    $stmt = $pdo->query("SELECT * FROM clients ORDER BY created_at DESC");
    return $stmt->fetchAll();
}

/**
 * Get a client by ID (PostgreSQL version)
 */
function getClientById($id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM clients WHERE client_id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

// Agent-Client relationship functions (PostgreSQL version)
function getAgentClients($agentId) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT c.client_id, c.name, c.client_number,
                                 p.policy_id as policy_number
                          FROM clients c
                          LEFT JOIN policies p ON c.client_id = p.client_id
                          WHERE p.agent_id = ?");
    $stmt->execute([$agentId]);
    return $stmt->fetchAll();
}

// Education details functions (Updated for new structure)
function getAgentEducation($agentId) {
    // Temporarily disabled - return empty array to avoid database errors
    return [];

    /* DISABLED CODE:
    global $pdo;
    $stmt = $pdo->prepare("SELECT ed.* FROM educationdetails ed
                          JOIN agents a ON ed.agent_id = a.id
                          WHERE a.id = ?
                          ORDER BY ed.year_completed DESC");
    $stmt->execute([$agentId]);
    return $stmt->fetchAll();
    */
}

// Document functions (FlutterFlow compatible)
function getAgentDocuments($agentId) {
    global $pdo;
    // FlutterFlow documents table has: id, created_at, user_id, path
    // We need to join with agents to find documents by agent
    try {
        $stmt = $pdo->prepare("SELECT d.*
                              FROM documents d
                              JOIN agents a ON d.user_id = a.user_id
                              WHERE a.id = ?");
        $stmt->execute([$agentId]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting agent documents: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if an agent number already exists in the database (PostgreSQL version)
 *
 * @param string $agentNumber Agent number to check
 * @return bool True if exists, false otherwise
 */
function agentNumberExists($agentNumber) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM agents WHERE agent_number = ?");
    $stmt->execute([$agentNumber]);
    return $stmt->fetchColumn() > 0;
}

/**
 * Add a new agent with optional education details (PostgreSQL version)
 * @param array $data The agent data
 * @param array $educationData Optional education data
 * @return bool True on success, false on failure
 */
function addAgent($data, $educationData = []) {
    global $pdo;

    try {
        $pdo->beginTransaction();

        $stmt = $pdo->prepare("INSERT INTO agents (agent_id, name, ic_number, gender, date_of_birth,
                              phone_number, address, beneficiary_phone, work_experience,
                              user_id, status, created_at, updated_at, photo)
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)");

        $stmt->execute([
            $data['agent_id'],
            $data['name'],
            $data['ic_number'],
            $data['gender'],
            $data['date_of_birth'],
            $data['phone_number'],
            $data['address'],
            $data['beneficiary_phone'] ?? null,
            $data['work_experience'] ?? null,
            $data['user_id'] ?? null,
            $data['status'] ?? 'Pending',
            $data['photo'] ?? null
        ]);

        // Insert education details if any
        if (!empty($educationData)) {
            foreach ($educationData as $edu) {
                if (!empty($edu['level'])) {
                    $stmt = $pdo->prepare("INSERT INTO educationdetails (agent_id, level, year_completed, institution_name)
                                         VALUES (?, ?, ?, ?)");
                    $stmt->execute([$data['agent_id'], $edu['level'], $edu['year'] ?? null, $edu['institution'] ?? null]);
                }
            }
        }

        $pdo->commit();
        return $data['agent_id'];

    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Error adding agent: " . $e->getMessage());
        return false;
    }
}

/**
 * Handle document uploads for an agent (PostgreSQL version)
 * @param string $agentId The agent ID
 * @return bool True on success, false on failure
 */
function handleDocumentUploads($agentId) {
    global $pdo;

    if (!isset($_FILES['documents']) || empty($_FILES['documents'])) {
        return;
    }

    // Get the user_id for this agent
    $stmt = $pdo->prepare("SELECT user_id FROM agents WHERE agent_id = ?");
    $stmt->execute([$agentId]);
    $agent = $stmt->fetch();

    if (!$agent || !$agent['user_id']) {
        error_log("Cannot upload documents: Agent $agentId has no associated user_id");
        return;
    }

    $uploadDir = 'uploads/documents/';
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0777, true)) {
            throw new Exception('Failed to create upload directory for documents');
        }
    }

    foreach ($_FILES['documents']['name'] as $type => $filename) {
        if (empty($filename)) continue;

        $tempFile = $_FILES['documents']['tmp_name'][$type];
        $errorCode = $_FILES['documents']['error'][$type];

        if ($errorCode === UPLOAD_ERR_OK) {
            // Generate unique filename
            $fileExtension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $newFileName = $agentId . '_' . $type . '_' . time() . '_' . $filename;
            $targetPath = $uploadDir . $newFileName;

            // Move the file
            if (move_uploaded_file($tempFile, $targetPath)) {
                // Insert into documents table (FlutterFlow structure: id, created_at, user_id, path)
                $stmt = $pdo->prepare("INSERT INTO documents (created_at, user_id, path)
                                      VALUES (NOW(), ?, ?)");
                $stmt->execute([$agent['user_id'], $targetPath]);
            }
        }
    }
}

/**
 * Get document type ID based on name (PostgreSQL version)
 * @param string $typeName The type name of the document
 * @return string The document type name
 */
function getDocumentTypeId($typeName) {
    // Return the type name directly for PostgreSQL version
    return $typeName;
}

/**
 * Get all agents with sorting (FlutterFlow compatible - joins with users for email)
 * @param string $sortColumn Column to sort by
 * @param string $sortOrder Sort direction (asc/desc)
 * @return array Array of agents with email from users table
 */
function getAllAgentsSorted($sortColumn = 'agent_number', $sortOrder = 'asc') {
    global $pdo;

    // Validate sort order
    $sortOrder = strtolower($sortOrder) === 'desc' ? 'DESC' : 'ASC';

    // Whitelist allowed columns to prevent SQL injection
    $allowedColumns = ['agent_number', 'name', 'ic_number', 'gender', 'date_of_birth', 'phone_number', 'created_at', 'status'];
    if (!in_array($sortColumn, $allowedColumns)) {
        $sortColumn = 'agent_number';
    }

    // Join with users table to get email
    $stmt = $pdo->prepare("SELECT a.*, u.email
                          FROM agents a
                          LEFT JOIN users u ON a.user_id = u.id
                          ORDER BY a.$sortColumn $sortOrder");
    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Add a new client to the database (PostgreSQL version)
 *
 * @param array $clientData The client data to insert
 * @return bool True if client was added successfully, false otherwise
 */
function addClient($clientData) {
    global $pdo;

    try {
        // Start transaction
        $pdo->beginTransaction();

        // Insert into clients table (matching FlutterFlow structure)
        $stmt = $pdo->prepare("INSERT INTO clients (
            client_id, policy_id, created_at, updated_at, name, ic_number, client_number,
            gender, date_of_birth, phone_number, email, address, marital_status, race,
            religion, nationality, occupation, exact_duties, nature_of_business,
            salary_yearly, company_name, company_address, weight, height,
            smoker, hospital_admission_history, status
        ) VALUES (?, ?, NOW(), NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        $stmt->execute([
            $clientData['client_id'],
            $clientData['policy_id'] ?? null,
            $clientData['name'],
            $clientData['ic_number'],
            $clientData['client_number'],
            $clientData['gender'],
            $clientData['date_of_birth'],
            $clientData['phone_number'],
            $clientData['email'],
            $clientData['address'],
            $clientData['marital_status'],
            $clientData['race'],
            $clientData['religion'],
            $clientData['nationality'],
            $clientData['occupation'],
            $clientData['exact_duties'],
            $clientData['nature_of_business'],
            $clientData['salary_yearly'],
            $clientData['company_name'],
            $clientData['company_address'],
            $clientData['weight'],
            $clientData['height'],
            $clientData['smoker'],
            $clientData['hospital_admission_history'],
            $clientData['status'] ?? ''
        ]);

        // Commit transaction
        $pdo->commit();

        return true;
    } catch (Exception $e) {
        // Rollback on error
        $pdo->rollBack();
        error_log("Error adding client: " . $e->getMessage());
        return false;
    }
}

/**
 * Upload a client document
 * 
 * @param string $clientId The client ID
 * @param string $documentType The type of document (ic, signature, bank_card)
 * @param array $file The uploaded file data
 * @return bool|string Returns the file path on success, false on failure
 */
function uploadClientDocument($clientId, $documentType, $file) {
    try {
        // Create upload directory if it doesn't exist
        $uploadDir = 'uploads/client_documents/' . $clientId;
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        // Generate unique filename
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $fileName = $documentType . '_' . time() . '.' . $fileExtension;
        $filePath = $uploadDir . '/' . $fileName;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return $filePath;
        }
        return false;
    } catch (Exception $e) {
        error_log("Error uploading client document: " . $e->getMessage());
        return false;
    }
}

/**
 * Save client document information to database (PostgreSQL version)
 *
 * @param string $clientId The client ID
 * @param string $documentType The type of document
 * @param string $filePath The path to the uploaded file
 * @return bool True if successful, false otherwise
 */
function saveClientDocument($clientId, $documentType, $filePath) {
    global $pdo;

    // For FlutterFlow structure, we need to get a user_id associated with this client
    // This is a simplified approach - you might need to adjust based on your business logic
    try {
        $stmt = $pdo->prepare("INSERT INTO documents (created_at, user_id, path) VALUES (NOW(), NULL, ?)");
        $stmt->execute([$filePath]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log("Error saving client document: " . $e->getMessage());
        return false;
    }
}

function clientIdExists($client_id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE client_id = ?");
    $stmt->execute([$client_id]);
    return $stmt->fetchColumn() > 0;
}

function getClientPolicies($client_id) {
    global $pdo;
    
    try {
        // Log the query being executed
        $query = "SELECT p.*, a.name as agent_name 
                  FROM policies p 
                  LEFT JOIN agents a ON p.agent_id = a.id
                  WHERE p.client_id = ?
                  ORDER BY p.created_at DESC";
        
        error_log("Running query: " . $query . " with client_id: " . $client_id);
        
        $stmt = $pdo->prepare($query);
        $stmt->execute([$client_id]);
        $policies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Debug the results
        foreach ($policies as $policy) {
            error_log("Policy: " . $policy['policy_id'] . ", Agent ID: " . $policy['agent_id'] . ", Agent Name: " . ($policy['agent_name'] ?? 'NULL'));
        }
        
        return $policies;
    } catch (Exception $e) {
        error_log("Error fetching client policies: " . $e->getMessage());
        return [];
    }
}

function deleteClient($clientId) {
    global $pdo;

    try {
        // Start transaction
        $pdo->beginTransaction();

        // 1. Delete from policies first
        $stmt = $pdo->prepare("DELETE FROM policies WHERE client_id = ?");
        $stmt->execute([$clientId]);

        // 2. Finally delete from clients table
        $stmt = $pdo->prepare("DELETE FROM clients WHERE client_id = ?");
        $stmt->execute([$clientId]);

        // Commit transaction
        $pdo->commit();

        return true;
    } catch (Exception $e) {
        // Rollback on error
        $pdo->rollBack();
        error_log("Error deleting client: " . $e->getMessage());
        return false;
    }
}

/**
 * Update policy status
 * @param string $policyId Policy ID
 * @param string $status New status (Active, Inactive, Pending, Expired, Cancelled)
 * @return bool Success status
 */
function updatePolicyStatus($policyId, $status) {
    global $pdo;

    $allowedStatuses = ['Active', 'Inactive', 'Pending', 'Expired', 'Cancelled'];

    if (!in_array($status, $allowedStatuses)) {
        return false;
    }

    try {
        $stmt = $pdo->prepare("UPDATE policies SET status = ?, updated_at = NOW() WHERE policy_id = ?");
        return $stmt->execute([$status, $policyId]);
    } catch (PDOException $e) {
        error_log("Error updating policy status: " . $e->getMessage());
        return false;
    }
}

?>
