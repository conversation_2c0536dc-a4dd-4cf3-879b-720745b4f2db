<?php
session_start();
require_once 'config.php';
require_once 'policies.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Testing the client ID existence functionality
if (isset($_GET['test_client_id'])) {
    $test_id = $_GET['test_client_id'];
    $exists = clientIdExists($test_id);
    echo "<div style='background: #f8f9fa; padding: 20px; margin: 20px; border-radius: 5px;'>";
    echo "<h3>Testing Client ID: " . htmlspecialchars($test_id) . "</h3>";
    echo "<p>Result: " . ($exists ? "ID EXISTS" : "ID DOES NOT EXIST") . "</p>";
    
    // Test the database query directly
    try {
        global $pdo;
        $stmt = $pdo->prepare("SELECT * FROM clients WHERE client_id = ?");
        $stmt->execute([$test_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p>Direct database query result: ";
        if ($result) {
            echo "FOUND A RECORD";
            echo "<pre>";
            print_r($result);
            echo "</pre>";
        } else {
            echo "NO RECORD FOUND";
        }
        echo "</p>";
        
        echo "<p>All client IDs in the database:</p><ul>";
        $all_stmt = $pdo->query("SELECT client_id, name FROM clients");
        $all_clients = $all_stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($all_clients as $client) {
            echo "<li>" . htmlspecialchars($client['client_id']) . " - " . htmlspecialchars($client['name']) . "</li>";
        }
        echo "</ul>";
    } catch (Exception $e) {
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
    
    echo "<p><a href='client_add.php'>Return to add client page</a></p>";
    echo "</div>";
    exit;
}

// Set page title
$pageTitle = 'ADD NEW CLIENT';

// Initialize variables
$errors = [];
$success = false;
$formData = [
    'client_id' => '',
    'name' => '',
    'ic_number' => '',
    'client_number' => '',
    'gender' => '',
    'date_of_birth' => '',
    'phone_number' => '',
    'email' => '',
    'address' => '',
    'marital_status' => '',
    'race' => '',
    'religion' => '',
    'nationality' => '',
    'occupation' => '',
    'exact_duties' => '',
    'nature_of_business' => '',
    'salary_yearly' => '',
    'company_name' => '',
    'company_address' => '',
    'weight' => '',
    'height' => '',
    'smoker' => '',
    'hospital_admission_history' => '',
    'plan' => '',
    'policy_number' => '',
    'basic_plan_rider' => '',
    'sum_covered' => '',
    'coverage_term' => '',
    'contribution' => '',
    'start_date' => date('Y-m-d'),
    'end_date' => date('Y-m-d', strtotime('+1 year'))
];

// Get all agents for the dropdown
$agents = getAllAgents();

// If form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get and validate form data
    $client_id = trim($_POST['client_id'] ?? '');
    $name = trim($_POST['name'] ?? '');
    $ic_number = preg_replace('/[^0-9]/', '', $_POST['ic_number'] ?? '');
    $client_number = trim($_POST['client_number'] ?? '');
    $gender = $_POST['gender'] ?? '';
    $date_of_birth = $_POST['date_of_birth'] ?? '';
    $phone_number = preg_replace('/[^0-9]/', '', $_POST['phone_number'] ?? '');
    $email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
    $address = trim($_POST['address'] ?? '');
    $marital_status = trim($_POST['marital_status'] ?? '');
    $race = trim($_POST['race'] ?? '');
    $religion = trim($_POST['religion'] ?? '');
    $nationality = trim($_POST['nationality'] ?? '');
    $occupation = trim($_POST['occupation'] ?? '');
    $exact_duties = trim($_POST['exact_duties'] ?? '');
    $nature_of_business = trim($_POST['nature_of_business'] ?? '');
    $salary_yearly = trim($_POST['salary_yearly'] ?? '');
    $company_name = trim($_POST['company_name'] ?? '');
    $company_address = trim($_POST['company_address'] ?? '');
    $weight = trim($_POST['weight'] ?? '');
    $height = trim($_POST['height'] ?? '');
    $smoker = $_POST['smoker'] ?? '';
    $hospital_admission_history = trim($_POST['hospital_admission_history'] ?? '');
    
    // Store form data for repopulating the form
    $formData = [
        'client_id' => $client_id,
        'name' => $name,
        'ic_number' => $ic_number,
        'client_number' => $client_number,
        'gender' => $gender,
        'date_of_birth' => $date_of_birth,
        'phone_number' => $phone_number,
        'email' => $_POST['email'] ?? '',
        'address' => $address,
        'marital_status' => $marital_status,
        'race' => $race,
        'religion' => $religion,
        'nationality' => $nationality,
        'occupation' => $occupation,
        'exact_duties' => $exact_duties,
        'nature_of_business' => $nature_of_business,
        'salary_yearly' => $salary_yearly,
        'company_name' => $company_name,
        'company_address' => $company_address,
        'weight' => $weight,
        'height' => $height,
        'smoker' => $smoker,
        'hospital_admission_history' => $hospital_admission_history
    ];
    
    // Validate required fields
    if (empty($name)) {
        $errors[] = "Name is required";
    }
    if (empty($ic_number)) {
        $errors[] = "IC Number is required";
    }
    if (empty($gender)) {
        $errors[] = "Gender is required";
    }
    if (empty($date_of_birth)) {
        $errors[] = "Date of birth is required";
    }
    if (empty($phone_number)) {
        $errors[] = "Phone number is required";
    }
    if (empty($_POST['email'])) {
        $errors[] = "Email is required";
    } else if (!$email) {
        $errors[] = "Please enter a valid email address";
    }
    if (empty($address)) {
        $errors[] = "Address is required";
    }
    
    // Check if client ID already exists (only if client ID is provided)
    if (empty($errors) && !empty($client_id) && clientIdExists($client_id)) {
        $errors[] = "Client ID already exists";
    }
    
    // Handle document uploads
    if (!empty($client_id)) {
        $documentTypes = ['ic', 'signature', 'bank_card'];
        foreach ($documentTypes as $type) {
            if (isset($_FILES[$type]) && $_FILES[$type]['error'] === UPLOAD_ERR_OK) {
                $filePath = uploadClientDocument($client_id, $type, $_FILES[$type]);
                if ($filePath) {
                    saveClientDocument($client_id, $type, $filePath);
                } else {
                    $errors[] = "Failed to upload " . str_replace('_', ' ', $type) . " document";
                }
            }
        }
    }
    
    // If no errors, insert into database
    if (empty($errors)) {
        try {
            // Begin transaction
            $pdo->beginTransaction();
            
            // Generate a temporary client ID if none provided
            if (empty($client_id)) {
                // Using PENDING prefix to identify pending clients
                $client_id = 'PENDING_' . time() . '_' . rand(1000, 9999);
            }
            
            // Add a status field to track pending clients
            $status = 'Pending';
            
            // Insert client data with status field
            $sql = "INSERT INTO clients (
                client_id, name, ic_number, gender, date_of_birth,
                phone_number, email, address, marital_status, race, religion,
                nationality, occupation, exact_duties, nature_of_business,
                salary_yearly, company_name, company_address, weight, height,
                smoker, hospital_admission_history, status
            ) VALUES (
                :client_id, :name, :ic_number, :gender, :date_of_birth,
                :phone_number, :email, :address, :marital_status, :race, :religion,
                :nationality, :occupation, :exact_duties, :nature_of_business,
                :salary_yearly, :company_name, :company_address, :weight, :height,
                :smoker, :hospital_admission_history, :status
            )";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':client_id' => $client_id,
                ':name' => $name,
                ':ic_number' => $ic_number,
                ':gender' => $gender,
                ':date_of_birth' => $date_of_birth,
                ':phone_number' => $phone_number,
                ':email' => $email,
                ':address' => $address,
                ':marital_status' => $marital_status,
                ':race' => $race,
                ':religion' => $religion,
                ':nationality' => $nationality,
                ':occupation' => $occupation,
                ':exact_duties' => $exact_duties,
                ':nature_of_business' => $nature_of_business,
                ':salary_yearly' => $salary_yearly,
                ':company_name' => $company_name,
                ':company_address' => $company_address,
                ':weight' => $weight,
                ':height' => $height,
                ':smoker' => $smoker,
                ':hospital_admission_history' => $hospital_admission_history,
                ':status' => $status
            ]);
            
            // Commit transaction
            $pdo->commit();
            
            // Redirect to client list page
            header('Location: clients.php?success=Client added successfully');
            exit;
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $errors[] = "Error adding client: " . $e->getMessage();
        }
    }
}

// Include layout header
include 'layout.php';
?>

<!-- Include Select2 CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
.detail-container {
    max-width: 1000px;
    margin-left: 400px;
    margin-bottom: 50px;
    padding: 30px;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
    border-radius: 15px;
}

.client-header {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 40px;
    text-align: center;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-section {
    margin-bottom: 40px;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
}

.section-title {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.detail-row {
    margin-bottom: 15px;
    display: flex;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    width: 180px;
    font-weight: 600;
    color: #495057;
    padding-top: 8px;
}

.detail-value {
    flex-grow: 1;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

.btn-container {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #2c3e50;
    border: none;
    color: white;
}

.btn-primary:hover {
    background-color: #1e2a37;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #6c757d;
    border: none;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.required {
    color: #e74a3b;
    margin-left: 4px;
}

.strict-numbers-only {
    -moz-appearance: textfield;
}

.strict-numbers-only::-webkit-outer-spin-button,
.strict-numbers-only::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Select2 Custom Styling */
.select2-container--default .select2-selection--single {
    height: 38px;
    padding: 5px 8px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 28px;
    color: #495057;
}

.select2-dropdown {
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.select2-search--dropdown .select2-search__field {
    padding: 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #2c3e50;
}

.btn-success {
    background-color: #2ecc71;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background-color: #27ae60;
    transform: translateY(-1px);
}

.me-2 {
    margin-right: 8px;
}

.me-3 {
    margin-right: 12px;
}

.p-3 {
    padding: 1rem;
}

.rounded {
    border-radius: 8px;
}

.bg-white {
    background-color: white;
}

.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.form-section .detail-row:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.form-section .detail-row .detail-value {
    padding: 0;
}

.form-section .detail-row {
    background: #f8f9fa;
    margin-bottom: 1rem;
    border: none;
}
</style>

<div class="detail-container">
    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
    </div>
<?php endif; ?>

    <div class="client-header">
        ADD NEW CLIENT
    </div>

    <form method="POST" action="client_add.php" enctype="multipart/form-data">
        <!-- Basic Information Section -->
        <div class="form-section">
            <div class="section-title">Basic Information</div>
            
            <div class="detail-row">
                <div class="detail-label">Name <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="name" required value="<?php echo htmlspecialchars($formData['name']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">IC Number <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="tel" class="form-control strict-numbers-only" name="ic_number" required value="<?php echo htmlspecialchars($formData['ic_number']); ?>" pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="20">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Client ID</div>
                <div class="detail-value">
                    <div class="input-group">
                        <input type="text" class="form-control" name="client_id" value="<?php echo htmlspecialchars($formData['client_id']); ?>" <?php echo !empty($formData['client_id']) ? 'readonly' : ''; ?>>
                        <?php if (!empty($formData['client_id'])): ?>
                            <div class="input-group-append">
                                <span class="input-group-text bg-light"><i class="fas fa-check text-success"></i> Auto-generated</span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <small class="form-text text-muted">Optional. Leave empty for pending client ID. You can enter any combination of letters and numbers.</small>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Gender <span class="required">*</span></div>
                <div class="detail-value">
                    <select class="form-control" name="gender" required>
                        <option value="">Select Gender</option>
                        <option value="Male" <?php echo $formData['gender'] === 'Male' ? 'selected' : ''; ?>>Male</option>
                        <option value="Female" <?php echo $formData['gender'] === 'Female' ? 'selected' : ''; ?>>Female</option>
                        <option value="Other" <?php echo $formData['gender'] === 'Other' ? 'selected' : ''; ?>>Other</option>
                    </select>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Date Of Birth <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="date" class="form-control" name="date_of_birth" required value="<?php echo htmlspecialchars($formData['date_of_birth']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Phone Number <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="tel" class="form-control strict-numbers-only" name="phone_number" required value="<?php echo htmlspecialchars($formData['phone_number']); ?>" pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="15">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Email <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="email" class="form-control" name="email" required value="<?php echo htmlspecialchars($formData['email']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Address <span class="required">*</span></div>
                <div class="detail-value">
                    <textarea class="form-control" name="address" required><?php echo htmlspecialchars($formData['address']); ?></textarea>
                </div>
            </div>
        </div>
        
        <!-- Personal Details Section -->
        <div class="form-section">
            <div class="section-title">Personal Details</div>
            
            <div class="detail-row">
                <div class="detail-label">Marital Status</div>
                <div class="detail-value">
                    <select class="form-control" name="marital_status">
                        <option value="">Select Marital Status</option>
                        <option value="Single" <?php echo $formData['marital_status'] === 'Single' ? 'selected' : ''; ?>>Single</option>
                        <option value="Married" <?php echo $formData['marital_status'] === 'Married' ? 'selected' : ''; ?>>Married</option>
                        <option value="Divorced" <?php echo $formData['marital_status'] === 'Divorced' ? 'selected' : ''; ?>>Divorced</option>
                        <option value="Widowed" <?php echo $formData['marital_status'] === 'Widowed' ? 'selected' : ''; ?>>Widowed</option>
                    </select>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Race</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="race" value="<?php echo htmlspecialchars($formData['race']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Religion</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="religion" value="<?php echo htmlspecialchars($formData['religion']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Nationality</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="nationality" value="<?php echo htmlspecialchars($formData['nationality']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Height (cm)</div>
                <div class="detail-value">
                    <input type="number" step="0.01" class="form-control" name="height" value="<?php echo htmlspecialchars($formData['height']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Weight (kg)</div>
                <div class="detail-value">
                    <input type="number" step="0.01" class="form-control" name="weight" value="<?php echo htmlspecialchars($formData['weight']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Smoker</div>
                <div class="detail-value">
                    <select class="form-control" name="smoker">
                        <option value="">Select Option</option>
                        <option value="Smoker" <?php echo $formData['smoker'] === 'Smoker' ? 'selected' : ''; ?>>Smoker</option>
                        <option value="Non-smoker" <?php echo $formData['smoker'] === 'Non-smoker' ? 'selected' : ''; ?>>Non-smoker</option>
                    </select>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Hospital Admission History</div>
                <div class="detail-value">
                    <textarea class="form-control" name="hospital_admission_history"><?php echo htmlspecialchars($formData['hospital_admission_history']); ?></textarea>
                </div>
            </div>
        </div>
        
        <!-- Employment Section -->
        <div class="form-section">
            <div class="section-title">Employment Information</div>
            
            <div class="detail-row">
                <div class="detail-label">Occupation</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="occupation" value="<?php echo htmlspecialchars($formData['occupation']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Exact Duties</div>
                <div class="detail-value">
                    <textarea class="form-control" name="exact_duties"><?php echo htmlspecialchars($formData['exact_duties']); ?></textarea>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Nature of Business</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="nature_of_business" value="<?php echo htmlspecialchars($formData['nature_of_business']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Yearly Salary</div>
                <div class="detail-value">
                    <input type="number" step="0.01" class="form-control" name="salary_yearly" value="<?php echo htmlspecialchars($formData['salary_yearly']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Company Name</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="company_name" value="<?php echo htmlspecialchars($formData['company_name']); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Company Address</div>
                <div class="detail-value">
                    <textarea class="form-control" name="company_address"><?php echo htmlspecialchars($formData['company_address']); ?></textarea>
                </div>
            </div>
        </div>

        <!-- Document Upload Section -->
        <div class="form-section">
            <div class="section-title">Documents</div>
            
            <div class="detail-row">
                <div class="detail-value">
                    <div class="d-flex justify-content-between align-items-center p-3 bg-white rounded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-pdf text-danger me-3" style="font-size: 24px;"></i>
                            <span>Photocopy of NRIC</span>
                        </div>
                        <button type="button" class="btn btn-success" onclick="document.getElementById('nric').click();">
                            <i class="fas fa-upload me-2"></i> Upload File
                        </button>
                        <input type="file" id="nric" name="nric" accept="application/pdf,image/*" style="display: none;">
                    </div>
                </div>
            </div>
            
            <div class="detail-row">
                <div class="detail-value">
                    <div class="d-flex justify-content-between align-items-center p-3 bg-white rounded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-pdf text-danger me-3" style="font-size: 24px;"></i>
                            <span>Picture of Signature</span>
                        </div>
                        <button type="button" class="btn btn-success" onclick="document.getElementById('signature').click();">
                            <i class="fas fa-upload me-2"></i> Upload File
                        </button>
                        <input type="file" id="signature" name="signature" accept="image/*" style="display: none;">
                    </div>
                </div>
            </div>
            
            <div class="detail-row">
                <div class="detail-value">
                    <div class="d-flex justify-content-between align-items-center p-3 bg-white rounded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-pdf text-danger me-3" style="font-size: 24px;"></i>
                            <span>Photocopy of Bank Card</span>
                        </div>
                        <button type="button" class="btn btn-success" onclick="document.getElementById('bank_card').click();">
                            <i class="fas fa-upload me-2"></i> Upload File
                        </button>
                        <input type="file" id="bank_card" name="bank_card" accept="image/*" style="display: none;">
                    </div>
                </div>
            </div>
        </div>

        <div class="btn-container">
            <a href="clients.php" class="btn btn-secondary">
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                Save Client
            </button>
        </div>
    </form>
</div>

<?php include 'layout_footer.php'; ?> 