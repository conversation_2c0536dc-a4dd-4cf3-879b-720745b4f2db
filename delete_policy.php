<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('HTTP/1.1 401 Unauthorized');
    echo "Not authorized";
    exit;
}

// Check if policy ID is provided
if (!isset($_POST['policy_id']) || empty($_POST['policy_id'])) {
    header('HTTP/1.1 400 Bad Request');
    echo "Policy ID is required";
    exit;
}

$policyId = $_POST['policy_id'];

try {
    // Start transaction
    $conn->begin_transaction();
    
    // Check if the policy_beneficiaries table exists - skip if not
    $tableExists = false;
    $result = $conn->query("SHOW TABLES LIKE 'policy_beneficiaries'");
    if ($result && $result->num_rows > 0) {
        $tableExists = true;
    }
    
    // Delete beneficiaries if the table exists
    if ($tableExists) {
        $stmt = $conn->prepare("DELETE FROM policy_beneficiaries WHERE policy_id = ?");
        $stmt->bind_param("s", $policyId);
        $stmt->execute();
    }
    
    // Delete the policy
    $stmt = $conn->prepare("DELETE FROM policies WHERE policy_id = ?");
    $stmt->bind_param("s", $policyId);
    $result = $stmt->execute();
    
    if ($result) {
        $conn->commit();
        // Return success response
        header('Content-Type: application/json');
        echo json_encode(['success' => true]);
    } else {
        throw new Exception("Failed to delete policy");
    }
} catch (Exception $e) {
    // Rollback transaction on error
    if ($conn && $conn->ping()) {
        $conn->rollback();
    }
    
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>