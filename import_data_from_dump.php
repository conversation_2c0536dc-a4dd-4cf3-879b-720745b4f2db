<?php
// import_data_from_dump.php - Import data from SQL dump files to Supabase

echo "<h1>Import Data from SQL Dump Files</h1>";

// Include the config file
require_once 'config.php';

// Function to parse and execute INSERT statements from dump files
function importFromDumpFile($filePath, $pdo) {
    if (!file_exists($filePath)) {
        echo "❌ File not found: $filePath<br>";
        return false;
    }
    
    $content = file_get_contents($filePath);
    if (!$content) {
        echo "❌ Could not read file: $filePath<br>";
        return false;
    }
    
    // Parse the INSERT statement
    if (preg_match('/INSERT INTO "public"\.("?\w+"?) \((.*?)\) VALUES \((.*?)\);/', $content, $matches)) {
        $tableName = trim($matches[1], '"');
        $columns = $matches[2];
        $values = $matches[3];
        
        // Parse columns
        $columnArray = array_map(function($col) {
            return trim($col, ' "');
        }, explode(',', $columns));
        
        // Parse values - this is a simplified parser
        $valueArray = [];
        $inQuotes = false;
        $currentValue = '';
        $quoteChar = '';
        
        for ($i = 0; $i < strlen($values); $i++) {
            $char = $values[$i];
            
            if (!$inQuotes && ($char === "'" || $char === '"')) {
                $inQuotes = true;
                $quoteChar = $char;
                continue;
            } elseif ($inQuotes && $char === $quoteChar) {
                // Check if it's an escaped quote
                if ($i + 1 < strlen($values) && $values[$i + 1] === $quoteChar) {
                    $currentValue .= $char;
                    $i++; // Skip the next quote
                    continue;
                } else {
                    $inQuotes = false;
                    continue;
                }
            } elseif (!$inQuotes && $char === ',') {
                $valueArray[] = trim($currentValue) === 'null' ? null : $currentValue;
                $currentValue = '';
                continue;
            }
            
            $currentValue .= $char;
        }
        
        // Add the last value
        if ($currentValue !== '') {
            $valueArray[] = trim($currentValue) === 'null' ? null : $currentValue;
        }
        
        // Create the INSERT query
        $placeholders = str_repeat('?,', count($columnArray) - 1) . '?';
        $query = "INSERT INTO $tableName (" . implode(', ', $columnArray) . ") VALUES ($placeholders)";
        
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute($valueArray);
            echo "✅ Imported data to table: $tableName<br>";
            return true;
        } catch (PDOException $e) {
            echo "❌ Error importing to $tableName: " . $e->getMessage() . "<br>";
            echo "Query: $query<br>";
            echo "Values: " . print_r($valueArray, true) . "<br>";
            return false;
        }
    } else {
        echo "❌ Could not parse INSERT statement in: $filePath<br>";
        return false;
    }
}

// Import data from all dump files
echo "<h2>Importing Data from SQL Dump Files</h2>";

$dumpFiles = [
    'sql dump/users_rows.sql' => 'users',
    'sql dump/agents_rows.sql' => 'agents',
    'sql dump/clients_rows.sql' => 'clients',
    'sql dump/policies_rows.sql' => 'policies',
    'sql dump/educationdetails_rows.sql' => 'educationdetails',
    'sql dump/documents_rows.sql' => 'documents',
    'sql dump/beneficiaries_rows.sql' => 'beneficiaries',
    'sql dump/photos_rows.sql' => 'photos'
];

$successCount = 0;
$totalFiles = count($dumpFiles);

foreach ($dumpFiles as $filePath => $tableName) {
    echo "<h3>Importing $tableName</h3>";
    
    if (importFromDumpFile($filePath, $pdo)) {
        $successCount++;
    }
}

echo "<h2>Import Summary</h2>";
echo "<p>Successfully imported $successCount out of $totalFiles files.</p>";

// Verify the imported data
echo "<h2>Verification</h2>";
try {
    $tables = ['users', 'agents', 'clients', 'policies'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "✅ Table '$table' has $count records<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error verifying data: " . $e->getMessage() . "<br>";
}

echo "<h2>Manual Import Instructions</h2>";
echo "<p>If the automatic import doesn't work perfectly, you can manually import the data:</p>";
echo "<ol>";
echo "<li>Open your Supabase dashboard</li>";
echo "<li>Go to the SQL Editor</li>";
echo "<li>Copy the content from each SQL dump file</li>";
echo "<li>Modify the INSERT statements to remove the 'public' schema prefix</li>";
echo "<li>Execute each statement manually</li>";
echo "</ol>";

echo "<p><strong>Example:</strong> Change<br>";
echo "<code>INSERT INTO \"public\".\"agents\" (...) VALUES (...);</code><br>";
echo "to<br>";
echo "<code>INSERT INTO agents (...) VALUES (...);</code></p>";
?>
