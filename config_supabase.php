<?php
// config_supabase.php - Supabase PostgreSQL Database connection and helper functions

// Supabase Configuration
// TODO: Replace these with your actual Supabase credentials
$supabase_host = "db.your-project-ref.supabase.co";  // Replace with your Supabase host
$supabase_port = "5432";
$supabase_dbname = "postgres";  // Default Supabase database name
$supabase_username = "postgres";  // Default Supabase username
$supabase_password = "your-database-password";  // Replace with your Supabase password

// Create PostgreSQL connection using PDO
try {
    $dsn = "pgsql:host=$supabase_host;port=$supabase_port;dbname=$supabase_dbname;sslmode=require";
    $pdo = new PDO($dsn, $supabase_username, $supabase_password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    // Set timezone to UTC (Supabase default)
    $pdo->exec("SET timezone = 'UTC'");
    
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Legacy mysqli connection variable for backward compatibility
// Note: This will be null since we're using PostgreSQL
$conn = null;

// Function to log admin activity (PostgreSQL version)
function logActivity($userId, $action, $details) {
    global $pdo;
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, timestamp) VALUES (?, ?, ?, ?, NOW())");
    $stmt->execute([$userId, $action, $details, $ip]);
}

// Authentication functions (PostgreSQL version)
function authenticateUser($username, $password) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        return $user;
    }
    return false;
}

// Agent functions (PostgreSQL version)
function getAllAgents() {
    global $pdo;
    $agents = array();
    
    $sql = "SELECT * FROM agents ORDER BY name ASC";
    $result = $pdo->query($sql);
    
    if ($result && $result->rowCount() > 0) {
        while ($row = $result->fetch()) {
            $agents[] = $row;
        }
    }
    
    return $agents;
}

function getAgentById($agentId) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM agents WHERE agent_id = ?");
    $stmt->execute([$agentId]);
    return $stmt->fetch();
}

function createAgent($data) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO agents (agent_id, name, ic_number, gender, date_of_birth, phone_number, address, 
                          beneficiary_phone, work_experience, user_id, status, created_at, updated_at) 
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
    $stmt->execute([
        $data['agent_id'] ?? null,
        $data['name'], 
        $data['ic_number'], 
        $data['gender'], 
        $data['date_of_birth'], 
        $data['phone_number'], 
        $data['address'],
        $data['beneficiary_phone'] ?? null, 
        $data['work_experience'] ?? null,
        $data['user_id'] ?? null,
        $data['status'] ?? 'Pending'
    ]);
    
    return $pdo->lastInsertId();
}

/**
 * Update agent details in the database (PostgreSQL version)
 */
function updateAgent($oldAgentId, $data) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Check if the agent ID has changed
        $newAgentId = $data['agent_id'];
        $idChanged = ($oldAgentId !== $newAgentId);
        
        // Build the update query
        $sql = "UPDATE agents SET 
                name = :name,
                ic_number = :ic_number,
                gender = :gender,
                date_of_birth = :date_of_birth,
                phone_number = :phone_number,
                address = :address,
                beneficiary_phone = :beneficiary_phone,
                work_experience = :work_experience,
                updated_at = NOW()";
        
        // Add photo to update if it's provided
        if (isset($data['photo'])) {
            $sql .= ", photo = :photo";
        }
        
        // If ID changed, update that too
        if ($idChanged) {
            $sql .= ", agent_id = :new_agent_id";
        }
        
        $sql .= " WHERE agent_id = :old_agent_id";
        
        $stmt = $pdo->prepare($sql);
        
        // Bind parameters
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':ic_number', $data['ic_number']);
        $stmt->bindParam(':gender', $data['gender']);
        $stmt->bindParam(':date_of_birth', $data['date_of_birth']);
        $stmt->bindParam(':phone_number', $data['phone_number']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':beneficiary_phone', $data['beneficiary_phone']);
        $stmt->bindParam(':work_experience', $data['work_experience']);
        $stmt->bindParam(':old_agent_id', $oldAgentId);
        
        if (isset($data['photo'])) {
            $stmt->bindParam(':photo', $data['photo']);
        }
        
        if ($idChanged) {
            $stmt->bindParam(':new_agent_id', $newAgentId);
        }
        
        $result = $stmt->execute();
        
        if (!$result) {
            $errorInfo = $stmt->errorInfo();
            error_log("SQL execution failed: " . print_r($errorInfo, true));
            throw new Exception("SQL execution failed: " . $errorInfo[2]);
        }
        
        // If the agent ID changed, update related tables
        if ($idChanged && $result) {
            // Update policies table
            $stmt = $pdo->prepare("UPDATE policies SET agent_id = :new_agent_id WHERE agent_id = :old_agent_id");
            $stmt->bindParam(':new_agent_id', $newAgentId);
            $stmt->bindParam(':old_agent_id', $oldAgentId);
            $stmt->execute();
            
            // Log the ID change
            if (isset($_SESSION['user_id'])) {
                logActivity($_SESSION['user_id'], "update_agent", "Updated agent. ID changed from {$oldAgentId} to {$newAgentId}");
            }
        } else if ($result) {
            if (isset($_SESSION['user_id'])) {
                logActivity($_SESSION['user_id'], "update_agent", "Updated agent {$oldAgentId}");
            }
        }
        
        $pdo->commit();
        return true;
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Database error in updateAgent: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("General error in updateAgent: " . $e->getMessage());
        return false;
    }
}

function deleteAgent($agentId) {
    global $pdo;
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // 1. Delete from policies
        $stmt = $pdo->prepare("DELETE FROM policies WHERE agent_id = ?");
        $stmt->execute([$agentId]);
        
        // 2. Delete from educationdetails
        $stmt = $pdo->prepare("DELETE FROM educationdetails WHERE agent_id = ?");
        $stmt->execute([$agentId]);
        
        // 3. Finally delete from agents table
        $stmt = $pdo->prepare("DELETE FROM agents WHERE agent_id = ?");
        $stmt->execute([$agentId]);
        
        // Commit transaction
        $pdo->commit();
        
        return true;
    } catch (Exception $e) {
        // Rollback on error
        $pdo->rollBack();
        error_log("Error deleting agent: " . $e->getMessage());
        return false;
    }
}

// Client functions (PostgreSQL version)
function getAllClients() {
    global $pdo;
    $clients = [];
    
    $stmt = $pdo->query("SELECT * FROM clients ORDER BY created_at DESC");
    return $stmt->fetchAll();
}

/**
 * Get a client by ID (PostgreSQL version)
 */
function getClientById($id) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM clients WHERE client_id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

// Education details functions (PostgreSQL version)
function getAgentEducation($agentId) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM educationdetails WHERE agent_id = ? ORDER BY year_completed DESC");
    $stmt->execute([$agentId]);
    return $stmt->fetchAll();
}

/**
 * Check if an agent ID already exists in the database (PostgreSQL version)
 */
function agentIdExists($agentId) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM agents WHERE agent_id = ?");
    $stmt->execute([$agentId]);
    return $stmt->fetchColumn() > 0;
}

/**
 * Add a new agent with optional education details (PostgreSQL version)
 */
function addAgent($data, $educationData = []) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        $stmt = $pdo->prepare("INSERT INTO agents (agent_id, name, ic_number, gender, date_of_birth, 
                              phone_number, address, beneficiary_phone, work_experience, 
                              user_id, status, created_at, updated_at, photo) 
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)");
        
        $stmt->execute([
            $data['agent_id'],
            $data['name'], 
            $data['ic_number'], 
            $data['gender'], 
            $data['date_of_birth'], 
            $data['phone_number'], 
            $data['address'],
            $data['beneficiary_phone'] ?? null,
            $data['work_experience'] ?? null,
            $data['user_id'] ?? null,
            $data['status'] ?? 'Pending',
            $data['photo'] ?? null
        ]);
        
        // Insert education details if any
        if (!empty($educationData)) {
            foreach ($educationData as $edu) {
                if (!empty($edu['level'])) {
                    $stmt = $pdo->prepare("INSERT INTO educationdetails (agent_id, level, year_completed, institution_name) 
                                         VALUES (?, ?, ?, ?)");
                    $stmt->execute([$data['agent_id'], $edu['level'], $edu['year'] ?? null, $edu['institution'] ?? null]);
                }
            }
        }
        
        $pdo->commit();
        return $data['agent_id'];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Error adding agent: " . $e->getMessage());
        return false;
    }
}

/**
 * Add a new client to the database (PostgreSQL version)
 */
function addClient($clientData) {
    global $pdo;
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Insert into clients table
        $stmt = $pdo->prepare("INSERT INTO clients (
            client_id, name, ic_number, client_number, gender, date_of_birth,
            phone_number, email, address, marital_status, race, religion,
            nationality, occupation, exact_duties, nature_of_business,
            salary_yearly, company_name, company_address, weight, height,
            smoker, hospital_admission_history, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
        
        $stmt->execute([
            $clientData['client_id'],
            $clientData['name'],
            $clientData['ic_number'],
            $clientData['client_number'],
            $clientData['gender'],
            $clientData['date_of_birth'],
            $clientData['phone_number'],
            $clientData['email'],
            $clientData['address'],
            $clientData['marital_status'],
            $clientData['race'],
            $clientData['religion'],
            $clientData['nationality'],
            $clientData['occupation'],
            $clientData['exact_duties'],
            $clientData['nature_of_business'],
            $clientData['salary_yearly'],
            $clientData['company_name'],
            $clientData['company_address'],
            $clientData['weight'],
            $clientData['height'],
            $clientData['smoker'],
            $clientData['hospital_admission_history'],
            $clientData['status'] ?? ''
        ]);
        
        // Commit transaction
        $pdo->commit();
        
        return true;
    } catch (Exception $e) {
        // Rollback on error
        $pdo->rollBack();
        error_log("Error adding client: " . $e->getMessage());
        return false;
    }
}

function clientIdExists($client_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE client_id = ?");
    $stmt->execute([$client_id]);
    return $stmt->fetchColumn() > 0;
}

function getClientPolicies($client_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT p.*, a.name as agent_name 
                              FROM policies p 
                              LEFT JOIN agents a ON p.agent_id = a.agent_id 
                              WHERE p.client_id = ?
                              ORDER BY p.created_at DESC");
        $stmt->execute([$client_id]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error fetching client policies: " . $e->getMessage());
        return [];
    }
}

function deleteClient($clientId) {
    global $pdo;
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // 1. Delete from policies first
        $stmt = $pdo->prepare("DELETE FROM policies WHERE client_id = ?");
        $stmt->execute([$clientId]);
        
        // 2. Finally delete from clients table
        $stmt = $pdo->prepare("DELETE FROM clients WHERE client_id = ?");
        $stmt->execute([$clientId]);
        
        // Commit transaction
        $pdo->commit();
        
        return true;
    } catch (Exception $e) {
        // Rollback on error
        $pdo->rollBack();
        error_log("Error deleting client: " . $e->getMessage());
        return false;
    }
}

/**
 * Get all agents with sorting (PostgreSQL version)
 */
function getAllAgentsSorted($sortColumn = 'agent_id', $sortOrder = 'asc') {
    global $pdo;
    
    // Validate sort order
    $sortOrder = strtolower($sortOrder) === 'desc' ? 'DESC' : 'ASC';
    
    // Whitelist allowed columns to prevent SQL injection
    $allowedColumns = ['agent_id', 'name', 'ic_number', 'gender', 'date_of_birth', 'phone_number', 'created_at', 'status'];
    if (!in_array($sortColumn, $allowedColumns)) {
        $sortColumn = 'agent_id';
    }
    
    $stmt = $pdo->prepare("SELECT * FROM agents ORDER BY $sortColumn $sortOrder");
    $stmt->execute();
    return $stmt->fetchAll();
}
?>
