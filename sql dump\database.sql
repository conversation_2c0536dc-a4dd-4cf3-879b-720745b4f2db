-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.admins (
  id integer NOT NULL DEFAULT nextval('admins_id_seq'::regclass),
  username character varying NOT NULL UNIQUE,
  password character varying NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT admins_pkey PRIMARY KEY (id)
);
CREATE TABLE public.agents (
  id integer NOT NULL DEFAULT nextval('agents_id_seq'::regclass),
  agent_number character varying UNIQUE,
  name character varying,
  ic_number character varying,
  gender character varying,
  date_of_birth date,
  phone_number character varying,
  address text,
  beneficiary_phone character varying,
  work_experience text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  status character varying,
  user_id uuid,
  photo character varying,
  CONSTRAINT agents_pkey PRIMARY KEY (id),
  CONSTRAINT agents_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.beneficiaries (
  beneficiary_id integer GENERATED ALWAYS AS IDENTITY NOT NULL,
  policy_id character varying,
  name character varying,
  ic_number character varying,
  relationship character varying,
  date_of_birth date,
  phone_number character varying,
  email character varying,
  address text,
  percentage integer,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT beneficiaries_pkey PRIMARY KEY (beneficiary_id),
  CONSTRAINT beneficiaries_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id)
);
CREATE TABLE public.clients (
  client_id character varying NOT NULL,
  policy_id character varying,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  name character varying,
  ic_number character varying,
  client_number character varying,
  gender character varying,
  date_of_birth date,
  phone_number character varying,
  email character varying,
  address text,
  marital_status character varying,
  race character varying,
  religion character varying,
  nationality character varying,
  occupation character varying,
  exact_duties text,
  nature_of_business text,
  salary_yearly character varying,
  company_name character varying,
  company_address text,
  weight character varying,
  height character varying,
  smoker character varying,
  hospital_admission_history text,
  status character varying,
  nric character varying,
  signature character varying,
  bankcard character varying,
  CONSTRAINT clients_pkey PRIMARY KEY (client_id)
);
CREATE TABLE public.educationdetails (
  id integer NOT NULL DEFAULT nextval('educationdetails_id_seq'::regclass),
  agent_id integer,
  level character varying,
  year_completed character varying,
  institution_name character varying,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT educationdetails_pkey PRIMARY KEY (id),
  CONSTRAINT educationdetails_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(id)
);
CREATE TABLE public.policies (
  policy_id character varying NOT NULL,
  client_id character varying,
  agent_id integer,
  plan_type character varying,
  basic_plan_rider character varying,
  sum_covered character varying,
  coverage_term character varying,
  contribution character varying,
  start_date date,
  end_date date,
  status character varying,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  policy_name character varying,
  CONSTRAINT policies_pkey PRIMARY KEY (policy_id),
  CONSTRAINT policies_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(id),
  CONSTRAINT policies_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(client_id)
);
CREATE TABLE public.users (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  created_at timestamp with time zone DEFAULT now(),
  email character varying NOT NULL UNIQUE,
  CONSTRAINT users_pkey PRIMARY KEY (id)
);