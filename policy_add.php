<?php
session_start();
require_once 'config.php';
require_once 'policies.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if client ID is provided
if (!isset($_GET['client_id']) || empty($_GET['client_id'])) {
    die("Client ID is required");
}

$client_id = $_GET['client_id'];
$client = getClientById($client_id);

if (!$client) {
    header('Location: clients.php?error=Client not found');
    exit;
}

// Get all agents for the dropdown
$agents = getAllAgents();

// Initialize variables
$errors = [];
$success = false;
$formData = [
    'agent_id' => '',
    'plan_type' => '',
    'policy_id' => '',
    'basic_plan_rider' => '',
    'sum_covered' => '',
    'coverage_term' => '',
    'contribution' => '',
    'start_date' => date('Y-m-d'),
    'end_date' => date('Y-m-d', strtotime('+1 year'))
];

// If form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Process policy form
    $policy = [
        'agent_id' => $_POST['agent_id_0'] ?? '',
        'plan_type' => $_POST['plan_type_0'] ?? '',
        'policy_id' => $_POST['policy_id_0'] ?? '',
        'basic_plan_rider' => $_POST['basic_plan_rider_0'] ?? '',
        'sum_covered' => $_POST['sum_covered_0'] ?? '',
        'coverage_term' => $_POST['coverage_term_0'] ?? '',
        'contribution' => $_POST['contribution_0'] ?? '',
        'start_date' => $_POST['start_date_0'] ?? date('Y-m-d'),
        'end_date' => $_POST['end_date_0'] ?? date('Y-m-d', strtotime('+1 year'))
    ];
    
    // Collect beneficiary data (if provided)
    $beneficiary = [
        'name' => $_POST['beneficiary_name'] ?? '',
        'ic_number' => $_POST['beneficiary_ic_number'] ?? '',
        'relationship' => $_POST['beneficiary_relationship'] ?? '',
        'date_of_birth' => $_POST['beneficiary_date_of_birth'] ?? '',
        'phone_number' => $_POST['beneficiary_phone_number'] ?? '',
        'email' => $_POST['beneficiary_email'] ?? '',
        'address' => $_POST['beneficiary_address'] ?? '',
        'percentage' => $_POST['beneficiary_percentage'] ?? ''
    ];
    
    // Store form data for repopulating the form
    $formData = $policy;
    
    // Debug submitted data
    error_log("Submitted agent_id: " . $policy['agent_id']);
    
    // Validate policy fields
    if (empty($policy['agent_id'])) {
        $errors[] = "Assigned agent is required";
    }
    if (empty($policy['plan_type'])) {
        $errors[] = "Plan type is required";
    }
    if (empty($policy['policy_id'])) {
        $errors[] = "Policy ID is required";
    }
    if (empty($policy['basic_plan_rider'])) {
        $errors[] = "Basic Plan/Rider is required";
    }
    if (empty($policy['sum_covered'])) {
        $errors[] = "Sum Covered is required";
    }
    if (empty($policy['coverage_term'])) {
        $errors[] = "Coverage Term is required";
    }
    if (empty($policy['contribution'])) {
        $errors[] = "Contribution is required";
    }
    if (empty($policy['start_date'])) {
        $errors[] = "Start Date is required";
    }
    if (empty($policy['end_date'])) {
        $errors[] = "End Date is required";
    }
    
    // If no errors, insert into database
    if (empty($errors)) {
        try {
            // Add client_id to policy data
            $policy['client_id'] = $client_id;
            $policy['status'] = 'Pending';
            
            // Direct database insertion to ensure agent_id is properly saved
            global $pdo;
            
            // Debug information before insert
            error_log("Attempting to add policy with agent_id: " . $policy['agent_id']);
            
            $sql = "INSERT INTO policies (
                client_id, plan_type, policy_id, basic_plan_rider,
                sum_covered, coverage_term, contribution, agent_id, 
                status, start_date, end_date
            ) VALUES (
                :client_id, :plan_type, :policy_id, :basic_plan_rider,
                :sum_covered, :coverage_term, :contribution, :agent_id, 
                :status, :start_date, :end_date
            )";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                ':client_id' => $policy['client_id'],
                ':plan_type' => $policy['plan_type'],
                ':policy_id' => $policy['policy_id'],
                ':basic_plan_rider' => $policy['basic_plan_rider'],
                ':sum_covered' => $policy['sum_covered'],
                ':coverage_term' => $policy['coverage_term'],
                ':contribution' => $policy['contribution'],
                ':agent_id' => $policy['agent_id'],
                ':status' => $policy['status'],
                ':start_date' => $policy['start_date'],
                ':end_date' => $policy['end_date']
            ]);
            
            if ($result && $stmt->rowCount() > 0) {
                // If beneficiary information is provided, add it as well
                if (!empty($beneficiary['name']) && !empty($beneficiary['ic_number'])) {
                    $beneficiary_sql = "INSERT INTO beneficiaries (
                        policy_id, name, ic_number, relationship, date_of_birth,
                        phone_number, email, address, percentage
                    ) VALUES (
                        :policy_id, :name, :ic_number, :relationship, :date_of_birth,
                        :phone_number, :email, :address, :percentage
                    )";
                    
                    $ben_stmt = $pdo->prepare($beneficiary_sql);
                    $ben_stmt->execute([
                        ':policy_id' => $policy['policy_id'],
                        ':name' => $beneficiary['name'],
                        ':ic_number' => $beneficiary['ic_number'],
                        ':relationship' => $beneficiary['relationship'],
                        ':date_of_birth' => $beneficiary['date_of_birth'] ?: null,
                        ':phone_number' => $beneficiary['phone_number'],
                        ':email' => $beneficiary['email'],
                        ':address' => $beneficiary['address'],
                        ':percentage' => $beneficiary['percentage'] ?: 100.00
                    ]);
                }
                
                // Successfully inserted, redirect to client view page
                header("Location: client_view.php?id={$client_id}&success=Policy added successfully");
                exit;
            } else {
                // Failed to insert record
                $errors[] = "Failed to add policy. Database error: " . print_r($stmt->errorInfo(), true);
                error_log("Failed to add policy: " . print_r($stmt->errorInfo(), true));
            }
        } catch (Exception $e) {
            error_log("Error adding policy: " . $e->getMessage());
            $errors[] = "Error adding policy: " . $e->getMessage();
        }
    }
}

// Set page title
$pageTitle = 'ADD POLICY';

// Include layout header
include 'layout.php';
?>

<!-- Include Select2 CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
.detail-container {
    max-width: 800px;
    margin-left: 400px;
    margin-bottom: 50px;
    padding: 30px;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
    border-radius: 15px;
}

.policy-header {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
    text-align: center;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.client-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.client-name {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 5px;
}

.client-id {
    color: #6c757d;
}

.form-section {
    margin-bottom: 40px;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
}

.section-title {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.detail-row {
    margin-bottom: 15px;
    display: flex;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    width: 180px;
    font-weight: 600;
    color: #495057;
    padding-top: 8px;
}

.detail-value {
    flex-grow: 1;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

.btn-container {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #2c3e50;
    border: none;
    color: white;
}

.btn-primary:hover {
    background-color: #1e2a37;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #6c757d;
    border: none;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.required {
    color: #e74a3b;
    margin-left: 4px;
}

/* Select2 Custom Styling */
.select2-container--default .select2-selection--single {
    height: 38px;
    padding: 5px 8px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 28px;
    color: #495057;
}

.select2-dropdown {
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.select2-search--dropdown .select2-search__field {
    padding: 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #2c3e50;
}

.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.pt-4 {
    padding-top: 1.5rem;
}

.border-top {
    border-top: 1px solid #dee2e6;
}
</style>

<div class="detail-container">
    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
    </div>
    <?php endif; ?>

    <div class="policy-header">
        ADD NEW POLICY
    </div>
    
    <div class="client-info">
        <div class="client-name"><?php echo htmlspecialchars($client['name']); ?></div>
        <div class="client-id">Client ID: <?php echo htmlspecialchars($client['client_id']); ?></div>
    </div>

    <form method="POST" action="policy_add.php?client_id=<?php echo htmlspecialchars($client_id); ?>">
        <!-- Policy Details Section -->
        <div class="form-section">
            <div class="section-title">Policy Details</div>
            
            <div id="policiesContainer">
                <div class="policy-form" data-policy-index="0" id="policy_form_0">
                    <div class="detail-row">
                        <div class="detail-label">Assigned Agent <span class="required">*</span></div>
                        <div class="detail-value">
                            <select class="form-control agent-select" name="agent_id_0" required>
                                <option value="">Select Agent</option>
                                <?php foreach ($agents as $agent): ?>
                                    <option value="<?php echo htmlspecialchars($agent['agent_id']); ?>" <?php echo ($formData['agent_id'] == $agent['agent_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($agent['name']); ?> (<?php echo htmlspecialchars($agent['agent_id']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Plan Type <span class="required">*</span></div>
                        <div class="detail-value">
                            <input type="text" class="form-control" name="plan_type_0" required value="<?php echo htmlspecialchars($formData['plan_type']); ?>">
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Policy ID <span class="required">*</span></div>
                        <div class="detail-value">
                            <input type="text" class="form-control" name="policy_id_0" required value="<?php echo htmlspecialchars($formData['policy_id']); ?>">
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Basic Plan/Rider <span class="required">*</span></div>
                        <div class="detail-value">
                            <textarea class="form-control" name="basic_plan_rider_0" required><?php echo htmlspecialchars($formData['basic_plan_rider']); ?></textarea>
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Sum Covered <span class="required">*</span></div>
                        <div class="detail-value">
                            <input type="number" step="0.01" class="form-control" name="sum_covered_0" required value="<?php echo htmlspecialchars($formData['sum_covered']); ?>">
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Coverage Term <span class="required">*</span></div>
                        <div class="detail-value">
                            <input type="text" class="form-control" name="coverage_term_0" required value="<?php echo htmlspecialchars($formData['coverage_term']); ?>">
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Contribution <span class="required">*</span></div>
                        <div class="detail-value">
                            <input type="number" step="0.01" class="form-control" name="contribution_0" required value="<?php echo htmlspecialchars($formData['contribution']); ?>">
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Start Date <span class="required">*</span></div>
                        <div class="detail-value">
                            <input type="date" class="form-control" name="start_date_0" required value="<?php echo htmlspecialchars($formData['start_date']); ?>">
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">End Date <span class="required">*</span></div>
                        <div class="detail-value">
                            <input type="date" class="form-control" name="end_date_0" required value="<?php echo htmlspecialchars($formData['end_date']); ?>">
                        </div>
                    </div>
                    <input type="hidden" name="policy_index[]" value="0">
                </div>
            </div>
        </div>
        
        <!-- Beneficiary Details Section (New) -->
        <div class="form-section">
            <div class="section-title">Beneficiary Details (Optional)</div>
            
            <div class="detail-row">
                <div class="detail-label">Name</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="beneficiary_name" value="">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">IC Number</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="beneficiary_ic_number" value="">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Relationship</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="beneficiary_relationship" value="">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Date of Birth</div>
                <div class="detail-value">
                    <input type="date" class="form-control" name="beneficiary_date_of_birth" value="">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Phone Number</div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="beneficiary_phone_number" value="">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Email</div>
                <div class="detail-value">
                    <input type="email" class="form-control" name="beneficiary_email" value="">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Address</div>
                <div class="detail-value">
                    <textarea class="form-control" name="beneficiary_address"></textarea>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Percentage</div>
                <div class="detail-value">
                    <input type="number" step="0.01" class="form-control" name="beneficiary_percentage" min="0" max="100" value="">
                </div>
            </div>
        </div>

        <div class="btn-container">
            <a href="client_view.php?id=<?php echo htmlspecialchars($client_id); ?>" class="btn btn-secondary">
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                Save Policy
            </button>
        </div>
    </form>
</div>

<script>
// Initialize Select2 for agent dropdown when document is ready
$(document).ready(function() {
    $('.agent-select').select2({
        placeholder: "Search agent by name or ID",
        allowClear: true,
        width: '100%'
    });
});
</script>

<?php include 'layout_footer.php'; ?> 