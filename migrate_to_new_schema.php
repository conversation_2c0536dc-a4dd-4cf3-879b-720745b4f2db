<?php
/**
 * Database Schema Migration Script
 * This script migrates your existing database to match the new schema structure
 */

require_once 'config.php';

echo "<h1>Database Schema Migration</h1>";
echo "<p>This script will update your database to match the new schema structure.</p>";

// Function to execute SQL with error handling
function executeSQL($pdo, $sql, $description) {
    try {
        $pdo->exec($sql);
        echo "✅ $description<br>";
        return true;
    } catch (PDOException $e) {
        echo "❌ Failed: $description - " . $e->getMessage() . "<br>";
        return false;
    }
}

echo "<h2>Step 1: Backup Current Data</h2>";
echo "<p>⚠️ <strong>Important:</strong> Make sure you have backed up your database before proceeding!</p>";

echo "<h2>Step 2: Create New Tables</h2>";

// Create admins table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS admins (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)";
executeSQL($pdo, $sql, "Created admins table");

echo "<h2>Step 3: Modify Existing Tables</h2>";

// Add new columns to clients table
$clientColumns = [
    "ALTER TABLE clients ADD COLUMN IF NOT EXISTS nric VARCHAR(50)",
    "ALTER TABLE clients ADD COLUMN IF NOT EXISTS signature VARCHAR(255)",
    "ALTER TABLE clients ADD COLUMN IF NOT EXISTS bankcard VARCHAR(255)"
];

foreach ($clientColumns as $sql) {
    executeSQL($pdo, $sql, "Added new column to clients table");
}

echo "<h2>Step 4: Update Agents Table Structure</h2>";

// Check if agents table needs restructuring
try {
    $stmt = $pdo->query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'agents' AND column_name IN ('id', 'agent_id', 'agent_number')");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasId = false;
    $hasAgentId = false;
    $hasAgentNumber = false;
    
    foreach ($columns as $column) {
        if ($column['column_name'] === 'id') $hasId = true;
        if ($column['column_name'] === 'agent_id') $hasAgentId = true;
        if ($column['column_name'] === 'agent_number') $hasAgentNumber = true;
    }
    
    echo "<p>Current agents table structure:</p>";
    echo "<ul>";
    echo "<li>Has 'id' column: " . ($hasId ? "✅ Yes" : "❌ No") . "</li>";
    echo "<li>Has 'agent_id' column: " . ($hasAgentId ? "✅ Yes" : "❌ No") . "</li>";
    echo "<li>Has 'agent_number' column: " . ($hasAgentNumber ? "✅ Yes" : "❌ No") . "</li>";
    echo "</ul>";
    
    if ($hasAgentId && !$hasId) {
        echo "<h3>Migrating agents table structure...</h3>";
        
        // Start transaction for complex migration
        $pdo->beginTransaction();
        
        try {
            // Step 1: Add new columns
            if (!$hasId) {
                executeSQL($pdo, "ALTER TABLE agents ADD COLUMN id SERIAL", "Added id column to agents");
            }
            
            if (!$hasAgentNumber) {
                executeSQL($pdo, "ALTER TABLE agents ADD COLUMN agent_number VARCHAR(50)", "Added agent_number column to agents");
                
                // Copy agent_id values to agent_number
                executeSQL($pdo, "UPDATE agents SET agent_number = agent_id WHERE agent_number IS NULL", "Copied agent_id to agent_number");
            }
            
            // Step 2: Update foreign key references in other tables
            echo "<h4>Updating foreign key references...</h4>";
            
            // Get agent mappings (agent_id -> new id)
            $stmt = $pdo->query("SELECT agent_id, id FROM agents WHERE agent_id IS NOT NULL");
            $agentMappings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            // Update policies table
            foreach ($agentMappings as $oldAgentId => $newId) {
                $stmt = $pdo->prepare("UPDATE policies SET agent_id = ? WHERE agent_id = ?");
                $stmt->execute([$newId, $oldAgentId]);
            }
            echo "✅ Updated policies table agent references<br>";
            
            // Update educationdetails table
            foreach ($agentMappings as $oldAgentId => $newId) {
                $stmt = $pdo->prepare("UPDATE educationdetails SET agent_id = ? WHERE agent_id = ?");
                $stmt->execute([$newId, $oldAgentId]);
            }
            echo "✅ Updated educationdetails table agent references<br>";
            
            // Step 3: Drop old constraints and add new ones
            executeSQL($pdo, "ALTER TABLE policies DROP CONSTRAINT IF EXISTS fk_policies_agent", "Dropped old policies constraint");
            executeSQL($pdo, "ALTER TABLE educationdetails DROP CONSTRAINT IF EXISTS fk_education_agent", "Dropped old education constraint");
            
            // Add new constraints
            executeSQL($pdo, "ALTER TABLE policies ADD CONSTRAINT fk_policies_agent FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL", "Added new policies constraint");
            executeSQL($pdo, "ALTER TABLE educationdetails ADD CONSTRAINT fk_education_agent FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE", "Added new education constraint");
            
            $pdo->commit();
            echo "✅ Successfully migrated agents table structure<br>";
            
        } catch (Exception $e) {
            $pdo->rollback();
            echo "❌ Migration failed: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "<p>✅ Agents table structure is already up to date.</p>";
    }
    
} catch (PDOException $e) {
    echo "❌ Error checking agents table structure: " . $e->getMessage() . "<br>";
}

echo "<h2>Step 5: Update Beneficiaries Table</h2>";

// Check if beneficiaries table needs updating
try {
    $stmt = $pdo->query("SELECT column_name FROM information_schema.columns WHERE table_name = 'beneficiaries' AND column_name = 'beneficiary_id'");
    $hasBeneficiaryId = $stmt->rowCount() > 0;
    
    if (!$hasBeneficiaryId) {
        echo "<h3>Updating beneficiaries table structure...</h3>";
        
        // Rename id to beneficiary_id and add new columns
        $beneficiaryUpdates = [
            "ALTER TABLE beneficiaries RENAME COLUMN id TO beneficiary_id",
            "ALTER TABLE beneficiaries ADD COLUMN IF NOT EXISTS policy_id VARCHAR(50)",
            "ALTER TABLE beneficiaries ADD COLUMN IF NOT EXISTS ic_number VARCHAR(20)",
            "ALTER TABLE beneficiaries ADD COLUMN IF NOT EXISTS date_of_birth DATE",
            "ALTER TABLE beneficiaries ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20)",
            "ALTER TABLE beneficiaries ADD COLUMN IF NOT EXISTS email VARCHAR(100)",
            "ALTER TABLE beneficiaries ADD COLUMN IF NOT EXISTS address TEXT",
            "ALTER TABLE beneficiaries ADD COLUMN IF NOT EXISTS percentage INTEGER",
            "ALTER TABLE beneficiaries DROP COLUMN IF EXISTS agent_id",
            "ALTER TABLE beneficiaries DROP COLUMN IF EXISTS phone"
        ];
        
        foreach ($beneficiaryUpdates as $sql) {
            executeSQL($pdo, $sql, "Updated beneficiaries table structure");
        }
        
        // Add foreign key constraint
        executeSQL($pdo, "ALTER TABLE beneficiaries ADD CONSTRAINT fk_beneficiaries_policy FOREIGN KEY (policy_id) REFERENCES policies(policy_id) ON DELETE CASCADE", "Added beneficiaries foreign key");
        
    } else {
        echo "<p>✅ Beneficiaries table structure is already up to date.</p>";
    }
    
} catch (PDOException $e) {
    echo "❌ Error updating beneficiaries table: " . $e->getMessage() . "<br>";
}

echo "<h2>Step 6: Create Default Admin User</h2>";

// Create default admin user if none exists
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM admins");
    $adminCount = $stmt->fetchColumn();
    
    if ($adminCount == 0) {
        $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admins (username, password) VALUES (?, ?)");
        if ($stmt->execute(['admin', $defaultPassword])) {
            echo "✅ Created default admin user (username: admin, password: admin123)<br>";
            echo "⚠️ <strong>Please change the default password immediately!</strong><br>";
        }
    } else {
        echo "✅ Admin users already exist<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error creating admin user: " . $e->getMessage() . "<br>";
}

echo "<h2>Migration Complete!</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>✅ Database Migration Summary</h3>";
echo "<p>Your database has been updated to match the new schema. Key changes:</p>";
echo "<ul>";
echo "<li>✅ Agents table now uses integer ID with separate agent_number field</li>";
echo "<li>✅ Clients table includes nric, signature, and bankcard fields</li>";
echo "<li>✅ Admins table created for admin authentication</li>";
echo "<li>✅ Beneficiaries table updated with new structure</li>";
echo "<li>✅ Foreign key relationships updated</li>";
echo "</ul>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Test your application to ensure everything works correctly</li>";
echo "<li>Change the default admin password</li>";
echo "<li>Update any custom code that references the old schema</li>";
echo "</ol>";
echo "</div>";
?>
