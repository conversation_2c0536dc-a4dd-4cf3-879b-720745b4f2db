<?php
/**
 * Debug the exact getAgentEducation function being called
 */

require_once 'config.php';

echo "<h1>Debugging getAgentEducation Function</h1>";

// First, let's check what the function looks like
echo "<h2>1. Function Source Code Check</h2>";

$reflection = new ReflectionFunction('getAgentEducation');
$filename = $reflection->getFileName();
$startLine = $reflection->getStartLine();
$endLine = $reflection->getEndLine();

echo "Function is defined in: $filename<br>";
echo "Lines: $startLine to $endLine<br>";

// Read the actual function source
$file = file($filename);
$functionCode = array_slice($file, $startLine - 1, $endLine - $startLine + 1);

echo "<h3>Current Function Code:</h3>";
echo "<pre>";
foreach ($functionCode as $lineNum => $line) {
    echo ($startLine + $lineNum) . ": " . htmlspecialchars($line);
}
echo "</pre>";

echo "<h2>2. Database Schema Check</h2>";
try {
    $stmt = $pdo->query("SELECT column_name FROM information_schema.columns WHERE table_name = 'agents' ORDER BY ordinal_position");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Agents table columns: " . implode(', ', $columns) . "<br>";
    
    $hasId = in_array('id', $columns);
    $hasOldAgentId = in_array('agent_id', $columns);
    
    echo "Has 'id' column: " . ($hasId ? "✅ Yes" : "❌ No") . "<br>";
    echo "Has old 'agent_id' column: " . ($hasOldAgentId ? "⚠️ Yes (needs migration)" : "✅ No") . "<br>";
    
} catch (Exception $e) {
    echo "❌ Error checking schema: " . $e->getMessage() . "<br>";
}

echo "<h2>3. Test Function Call</h2>";
try {
    // Get first agent
    $stmt = $pdo->query("SELECT id FROM agents LIMIT 1");
    $agent = $stmt->fetch();
    
    if ($agent) {
        $agentId = $agent['id'];
        echo "Testing with agent ID: $agentId<br>";
        
        // This is the exact call that's failing
        echo "Calling getAgentEducation($agentId)...<br>";
        $education = getAgentEducation($agentId);
        
        echo "✅ SUCCESS! Function executed without errors<br>";
        echo "Found " . count($education) . " education records<br>";
        
    } else {
        echo "⚠️ No agents found in database<br>";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "Stack trace:<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>4. Check for Multiple Config Files</h2>";
$configFiles = glob('config*.php');
echo "Found config files: " . implode(', ', $configFiles) . "<br>";

foreach ($configFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $hasGetAgentEducation = strpos($content, 'function getAgentEducation') !== false;
        $hasOldQuery = strpos($content, 'a.agent_id') !== false;
        
        echo "$file: ";
        echo "Has getAgentEducation: " . ($hasGetAgentEducation ? "Yes" : "No") . ", ";
        echo "Has old query: " . ($hasOldQuery ? "⚠️ Yes" : "✅ No") . "<br>";
    }
}

?>
