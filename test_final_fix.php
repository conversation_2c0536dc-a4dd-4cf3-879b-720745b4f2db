<?php
/**
 * Final test to confirm the fix works
 */

require_once 'config.php';

echo "<h1>Final Fix Test</h1>";

$agentId = 4; // Using the same agent ID from your test

echo "<h2>Testing getAgentEducation($agentId)</h2>";

try {
    $education = getAgentEducation($agentId);
    echo "✅ SUCCESS! getAgentEducation() executed without errors<br>";
    echo "Found " . count($education) . " education records<br>";
    
    if (count($education) > 0) {
        echo "<h3>Education Records:</h3>";
        echo "<table border='1'>";
        echo "<tr><th>Level</th><th>Year</th><th>Institution</th></tr>";
        foreach ($education as $edu) {
            echo "<tr>";
            echo "<td>" . ($edu['level'] ?? 'N/A') . "</td>";
            echo "<td>" . ($edu['year_completed'] ?? 'N/A') . "</td>";
            echo "<td>" . ($edu['institution_name'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No education records found for this agent (this is normal if none were added).<br>";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "<br>";
    echo "The fix did not work.<br>";
}

echo "<h2>Testing Other Functions</h2>";

try {
    $agent = getAgentById($agentId);
    echo "✅ getAgentById($agentId): " . ($agent ? "Found " . $agent['name'] : "Not found") . "<br>";
    
    $documents = getAgentDocuments($agentId);
    echo "✅ getAgentDocuments($agentId): Found " . count($documents) . " documents<br>";
    
} catch (Exception $e) {
    echo "❌ Error in other functions: " . $e->getMessage() . "<br>";
}

echo "<h2>✅ Test Complete</h2>";
echo "<p><strong>If you see this without errors, you can now access agent_view.php successfully!</strong></p>";
echo "<p><a href='agent_view.php?id=$agentId'>Test agent_view.php with ID $agentId</a></p>";

?>
