<?php
// test_agent_view.php - Test agent view functionality

session_start();
require_once 'config.php';

echo "<h1>Test Agent View Functionality</h1>";

// Simulate admin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';

echo "<h2>Testing Agent View Functions</h2>";

// Test 1: Check available agents
echo "<h3>1. Available Agents</h3>";
try {
    $agents = getAllAgents();
    echo "✅ Found " . count($agents) . " agents<br>";
    
    if (count($agents) > 0) {
        echo "<strong>Available agents for testing:</strong><br>";
        foreach ($agents as $agent) {
            $agentId = $agent['agent_id'] ?? $agent['id'];
            $agentName = $agent['name'] ?? 'No name';
            $email = $agent['email'] ?? 'No email';
            echo "- ID: $agentId, Name: $agentName, Email: $email<br>";
        }
    } else {
        echo "⚠️ No agents found. You need to register some agents first.<br>";
    }
} catch (Exception $e) {
    echo "❌ Error getting agents: " . $e->getMessage() . "<br>";
}

// Test 2: Test getAgentById function
echo "<h3>2. Testing getAgentById() Function</h3>";
try {
    $agents = getAllAgents();
    if (count($agents) > 0) {
        $firstAgent = $agents[0];
        $testId = $firstAgent['agent_id'] ?? $firstAgent['id'];
        
        echo "Testing with agent ID: $testId<br>";
        $agent = getAgentById($testId);
        
        if ($agent) {
            echo "✅ getAgentById() works!<br>";
            echo "<strong>Agent details:</strong><br>";
            echo "- ID: " . ($agent['id'] ?? 'N/A') . "<br>";
            echo "- Agent ID: " . ($agent['agent_id'] ?? 'Not assigned') . "<br>";
            echo "- Name: " . ($agent['name'] ?? 'No name') . "<br>";
            echo "- Email: " . ($agent['email'] ?? 'No email') . "<br>";
            echo "- Phone: " . ($agent['phone_number'] ?? 'No phone') . "<br>";
            echo "- Status: " . ($agent['status'] ?? 'No status') . "<br>";
        } else {
            echo "❌ getAgentById() returned no data<br>";
        }
    } else {
        echo "⚠️ No agents to test with<br>";
    }
} catch (Exception $e) {
    echo "❌ Error in getAgentById(): " . $e->getMessage() . "<br>";
}

// Test 3: Test getAgentEducation function
echo "<h3>3. Testing getAgentEducation() Function</h3>";
try {
    $agents = getAllAgents();
    if (count($agents) > 0) {
        $firstAgent = $agents[0];
        $testId = $firstAgent['agent_id'] ?? $firstAgent['id'];
        
        $education = getAgentEducation($testId);
        echo "✅ getAgentEducation() returned " . count($education) . " education records<br>";
        
        if (count($education) > 0) {
            echo "<strong>Education details:</strong><br>";
            foreach ($education as $edu) {
                echo "- Level: {$edu['level']}, Year: {$edu['year_completed']}, Institution: {$edu['institution_name']}<br>";
            }
        } else {
            echo "ℹ️ No education details found for this agent<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error in getAgentEducation(): " . $e->getMessage() . "<br>";
}

// Test 4: Test getAgentDocuments function
echo "<h3>4. Testing getAgentDocuments() Function</h3>";
try {
    $agents = getAllAgents();
    if (count($agents) > 0) {
        $firstAgent = $agents[0];
        $testId = $firstAgent['agent_id'] ?? $firstAgent['id'];
        
        $documents = getAgentDocuments($testId);
        echo "✅ getAgentDocuments() returned " . count($documents) . " document records<br>";
        
        if (count($documents) > 0) {
            echo "<strong>Documents:</strong><br>";
            foreach ($documents as $doc) {
                echo "- Path: {$doc['path']}, Created: {$doc['created_at']}<br>";
            }
        } else {
            echo "ℹ️ No documents found for this agent<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error in getAgentDocuments(): " . $e->getMessage() . "<br>";
}

echo "<h2>Test Agent View Page</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>✅ Ready to Test Agent View</h3>";

if (count($agents ?? []) > 0) {
    $firstAgent = $agents[0];
    $testId = $firstAgent['agent_id'] ?? $firstAgent['id'];
    
    echo "<p>You can now test the agent view page:</p>";
    echo "<ul>";
    echo "<li><strong>Test with agent ID:</strong> <a href='agent_view.php?id=$testId' target='_blank'>View Agent $testId</a></li>";
    echo "<li><strong>Or go to agents list:</strong> <a href='agents.php' target='_blank'>agents.php</a> and click the view button</li>";
    echo "</ul>";
} else {
    echo "<p>⚠️ No agents available to test with. You need to:</p>";
    echo "<ul>";
    echo "<li>Register some agents in your FlutterFlow app</li>";
    echo "<li>Or manually insert test data</li>";
    echo "</ul>";
}
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 20px 0;'>";
echo "<h3>🔧 What Was Fixed</h3>";
echo "<ul>";
echo "<li><strong>getAgentById():</strong> Now joins with users table to get email</li>";
echo "<li><strong>getAgentEducation():</strong> Updated for new table structure</li>";
echo "<li><strong>Agent ID display:</strong> Shows 'Not Assigned Yet' if agent_id is null</li>";
echo "<li><strong>Removed missing fields:</strong> beneficiary_name, beneficiary_ic (not in your schema)</li>";
echo "<li><strong>Added new fields:</strong> status, created_at</li>";
echo "</ul>";
echo "</div>";
?>
