<?php
// test_client_view.php - Test client view functionality

session_start();
require_once 'config.php';

echo "<h1>Test Client View Functionality</h1>";

// Simulate admin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';

echo "<h2>Testing Client View Functions</h2>";

// Test 1: Check available clients
echo "<h3>1. Available Clients</h3>";
try {
    $clients = getAllClients();
    echo "✅ Found " . count($clients) . " clients<br>";
    
    if (count($clients) > 0) {
        echo "<strong>Available clients for testing:</strong><br>";
        foreach ($clients as $client) {
            $clientId = $client['client_id'];
            $clientName = $client['name'] ?? 'No name';
            $email = $client['email'] ?? 'No email';
            echo "- ID: $clientId, Name: $clientName, Email: $email<br>";
        }
    } else {
        echo "⚠️ No clients found. You need to add some clients first.<br>";
    }
} catch (Exception $e) {
    echo "❌ Error getting clients: " . $e->getMessage() . "<br>";
}

// Test 2: Test getClientById function
echo "<h3>2. Testing getClientById() Function</h3>";
try {
    $clients = getAllClients();
    if (count($clients) > 0) {
        $firstClient = $clients[0];
        $testId = $firstClient['client_id'];
        
        echo "Testing with client ID: $testId<br>";
        $client = getClientById($testId);
        
        if ($client) {
            echo "✅ getClientById() works!<br>";
            echo "<strong>Client details:</strong><br>";
            echo "- Client ID: " . ($client['client_id'] ?? 'N/A') . "<br>";
            echo "- Name: " . ($client['name'] ?? 'No name') . "<br>";
            echo "- Email: " . ($client['email'] ?? 'No email') . "<br>";
            echo "- Phone: " . ($client['phone_number'] ?? 'No phone') . "<br>";
            echo "- Address: " . ($client['address'] ?? 'No address') . "<br>";
        } else {
            echo "❌ getClientById() returned no data<br>";
        }
    } else {
        echo "⚠️ No clients to test with<br>";
    }
} catch (Exception $e) {
    echo "❌ Error in getClientById(): " . $e->getMessage() . "<br>";
}

// Test 3: Test getClientPolicies function
echo "<h3>3. Testing getClientPolicies() Function</h3>";
try {
    $clients = getAllClients();
    if (count($clients) > 0) {
        $firstClient = $clients[0];
        $testId = $firstClient['client_id'];
        
        $policies = getClientPolicies($testId);
        echo "✅ getClientPolicies() returned " . count($policies) . " policy records<br>";
        
        if (count($policies) > 0) {
            echo "<strong>Policies:</strong><br>";
            foreach ($policies as $policy) {
                $agentName = $policy['agent_name'] ?? 'No agent assigned';
                echo "- Policy ID: {$policy['policy_id']}, Agent: $agentName, Plan: {$policy['plan_type']}<br>";
            }
        } else {
            echo "ℹ️ No policies found for this client<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error in getClientPolicies(): " . $e->getMessage() . "<br>";
}

// Test 4: Check client-agent relationships
echo "<h3>4. Testing Client-Agent Relationships</h3>";
try {
    $stmt = $pdo->query("SELECT c.client_id, c.name as client_name, p.agent_id, a.name as agent_name 
                        FROM clients c 
                        LEFT JOIN policies p ON c.client_id = p.client_id 
                        LEFT JOIN agents a ON p.agent_id = a.agent_id 
                        LIMIT 5");
    $relationships = $stmt->fetchAll();
    
    echo "✅ Found " . count($relationships) . " client-agent relationships:<br>";
    foreach ($relationships as $rel) {
        $agentInfo = $rel['agent_id'] ? "Agent ID: {$rel['agent_id']}, Name: {$rel['agent_name']}" : "No agent assigned";
        echo "- Client {$rel['client_id']} ({$rel['client_name']}): $agentInfo<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking relationships: " . $e->getMessage() . "<br>";
}

// Test 5: Check database structure
echo "<h3>5. Checking Database Structure</h3>";
try {
    // Check clients table structure
    $stmt = $pdo->query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'clients' ORDER BY ordinal_position");
    $columns = $stmt->fetchAll();
    
    echo "✅ Clients table has " . count($columns) . " columns:<br>";
    foreach ($columns as $col) {
        echo "- {$col['column_name']} ({$col['data_type']})<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking database structure: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Client View Page</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>✅ Ready to Test Client View</h3>";

if (count($clients ?? []) > 0) {
    $firstClient = $clients[0];
    $testId = $firstClient['client_id'];
    
    echo "<p>You can now test the client view page:</p>";
    echo "<ul>";
    echo "<li><strong>Test with client ID:</strong> <a href='client_view.php?id=$testId' target='_blank'>View Client $testId</a></li>";
    echo "<li><strong>Or go to clients list:</strong> <a href='clients.php' target='_blank'>clients.php</a> and click the view button</li>";
    echo "</ul>";
} else {
    echo "<p>⚠️ No clients available to test with. You need to:</p>";
    echo "<ul>";
    echo "<li>Add some clients in your FlutterFlow app</li>";
    echo "<li>Or manually insert test data</li>";
    echo "</ul>";
}
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 15px; border-left: 4px solid #2196f3; margin: 20px 0;'>";
echo "<h3>📋 Client View Features</h3>";
echo "<p>The client_view.php should display:</p>";
echo "<ul>";
echo "<li><strong>Basic Info:</strong> Name, IC, Client ID, Gender, DOB, Phone, Email, Address</li>";
echo "<li><strong>Personal Details:</strong> Marital status, race, religion, nationality, height, weight, smoker</li>";
echo "<li><strong>Employment Info:</strong> Occupation, duties, business nature, salary</li>";
echo "<li><strong>Policies:</strong> List of policies with agent assignments</li>";
echo "<li><strong>Actions:</strong> Edit, Delete, Print buttons</li>";
echo "</ul>";
echo "</div>";
?>
