<?php
// test_clients_page.php - Test clients page functionality

session_start();
require_once 'config.php';

echo "<h1>Test Clients Page Functionality</h1>";

// Simulate admin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';

echo "<h2>Testing Clients Functions</h2>";

// Test 1: Test getClientsWithPolicyData function
echo "<h3>1. Testing getClientsWithPolicyData() function</h3>";
try {
    // Include the function from clients.php
    include_once 'clients.php';
    
    $clients = getClientsWithPolicyData();
    echo "✅ getClientsWithPolicyData() returned " . count($clients) . " clients<br>";
    
    if (count($clients) > 0) {
        echo "<strong>Sample client data with policy counts:</strong><br>";
        $firstClient = $clients[0];
        echo "<pre>";
        echo "Client ID: " . ($firstClient['client_id'] ?? 'N/A') . "\n";
        echo "Name: " . ($firstClient['name'] ?? 'N/A') . "\n";
        echo "Email: " . ($firstClient['email'] ?? 'N/A') . "\n";
        echo "Total Policies: " . ($firstClient['total_policies'] ?? 0) . "\n";
        echo "Active Policies: " . ($firstClient['active_policies'] ?? 0) . "\n";
        echo "Pending Policies: " . ($firstClient['pending_policies'] ?? 0) . "\n";
        echo "</pre>";
    } else {
        echo "ℹ️ No clients found in database<br>";
    }
} catch (Exception $e) {
    echo "❌ Error in getClientsWithPolicyData(): " . $e->getMessage() . "<br>";
}

// Test 2: Test getAllClients function (for comparison)
echo "<h3>2. Testing getAllClients() function</h3>";
try {
    $allClients = getAllClients();
    echo "✅ getAllClients() returned " . count($allClients) . " clients<br>";
    
    if (count($allClients) > 0) {
        echo "<strong>Basic client data:</strong><br>";
        foreach (array_slice($allClients, 0, 3) as $client) {
            echo "- {$client['client_id']}: {$client['name']} ({$client['email']})<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error in getAllClients(): " . $e->getMessage() . "<br>";
}

// Test 3: Test policy counting logic
echo "<h3>3. Testing Policy Counting Logic</h3>";
try {
    $stmt = $pdo->query("SELECT 
        c.client_id, 
        c.name,
        COUNT(p.policy_id) as total_policies,
        COUNT(CASE WHEN LOWER(TRIM(p.status)) = 'active' THEN 1 END) as active_policies,
        COUNT(CASE WHEN LOWER(TRIM(p.status)) = 'pending' THEN 1 END) as pending_policies
        FROM clients c
        LEFT JOIN policies p ON c.client_id = p.client_id
        GROUP BY c.client_id, c.name
        LIMIT 5");
    
    $policyStats = $stmt->fetchAll();
    
    echo "✅ Policy counting query works. Sample results:<br>";
    foreach ($policyStats as $stat) {
        echo "- Client {$stat['client_id']} ({$stat['name']}): {$stat['total_policies']} total, {$stat['active_policies']} active, {$stat['pending_policies']} pending<br>";
    }
} catch (Exception $e) {
    echo "❌ Error in policy counting: " . $e->getMessage() . "<br>";
}

// Test 4: Test delete functionality
echo "<h3>4. Testing Delete Client Function</h3>";
try {
    $clients = getAllClients();
    if (count($clients) > 0) {
        $testClient = $clients[0];
        echo "✅ Delete functionality available for testing<br>";
        echo "- Test client: {$testClient['client_id']} ({$testClient['name']})<br>";
        echo "- Delete endpoint: delete_client.php<br>";
        echo "- Method: POST with JSON payload<br>";
    } else {
        echo "⚠️ No clients available to test delete functionality<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking delete functionality: " . $e->getMessage() . "<br>";
}

// Test 5: Check database relationships
echo "<h3>5. Testing Database Relationships</h3>";
try {
    $stmt = $pdo->query("SELECT 
        c.client_id,
        c.name as client_name,
        COUNT(p.policy_id) as policy_count,
        STRING_AGG(DISTINCT a.name, ', ') as agent_names
        FROM clients c
        LEFT JOIN policies p ON c.client_id = p.client_id
        LEFT JOIN agents a ON p.agent_id = a.agent_id
        GROUP BY c.client_id, c.name
        LIMIT 5");
    
    $relationships = $stmt->fetchAll();
    
    echo "✅ Client-Policy-Agent relationships:<br>";
    foreach ($relationships as $rel) {
        $agents = $rel['agent_names'] ?: 'No agents assigned';
        echo "- Client {$rel['client_id']} ({$rel['client_name']}): {$rel['policy_count']} policies, Agents: $agents<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking relationships: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Clients Page</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>✅ Ready to Test Clients Page</h3>";
echo "<p>The clients page functions are working. Now you can:</p>";
echo "<ol>";
echo "<li><strong>Visit clients.php:</strong> <a href='clients.php' target='_blank'>Open clients.php</a></li>";
echo "<li><strong>Check client listing</strong> with policy counts</li>";
echo "<li><strong>Test view/edit/delete</strong> buttons</li>";
echo "<li><strong>Test sorting</strong> by clicking column headers</li>";
echo "<li><strong>Test search/filter</strong> functionality</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 20px 0;'>";
echo "<h3>🔧 What Was Fixed</h3>";
echo "<ul>";
echo "<li><strong>getClientsWithPolicyData():</strong> Converted from mysqli to PDO</li>";
echo "<li><strong>Policy counting:</strong> Uses efficient GROUP BY query</li>";
echo "<li><strong>Delete functionality:</strong> Updated for PostgreSQL</li>";
echo "<li><strong>Database relationships:</strong> Proper JOIN queries</li>";
echo "<li><strong>Error handling:</strong> Better exception handling</li>";
echo "</ul>";
echo "</div>";

if (count($clients ?? []) === 0) {
    echo "<div style='background: #ffebee; padding: 15px; border-left: 4px solid #f44336; margin: 20px 0;'>";
    echo "<h3>⚠️ No Clients Found</h3>";
    echo "<p>Your clients table is empty. This could mean:</p>";
    echo "<ul>";
    echo "<li>No clients have been added yet in FlutterFlow</li>";
    echo "<li>Data import didn't work properly</li>";
    echo "<li>Database connection issues</li>";
    echo "</ul>";
    echo "<p><strong>Next step:</strong> Add some test clients or check your FlutterFlow app</p>";
    echo "</div>";
}
?>
