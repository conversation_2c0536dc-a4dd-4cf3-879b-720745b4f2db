<?php
// test_admin_login.php - Test admin authentication

echo "<h1>Test Admin Login System</h1>";

require_once 'config.php';

echo "<h2>Checking Admin Table</h2>";

try {
    // Check if admins table exists and has data
    $stmt = $pdo->query("SELECT COUNT(*) FROM admins");
    $adminCount = $stmt->fetchColumn();
    echo "✅ Admins table has $adminCount records<br>";
    
    if ($adminCount > 0) {
        $stmt = $pdo->query("SELECT id, username, created_at FROM admins");
        $admins = $stmt->fetchAll();
        echo "<strong>Admin accounts:</strong><br>";
        foreach ($admins as $admin) {
            echo "- ID: {$admin['id']}, Username: {$admin['username']}, Created: {$admin['created_at']}<br>";
        }
    } else {
        echo "❌ No admin accounts found. You need to add an admin first.<br>";
        echo "<h3>To add an admin manually:</h3>";
        echo "<p>Run this SQL in your Supabase SQL Editor:</p>";
        echo "<code>INSERT INTO admins (username, password, created_at) VALUES ('admin', crypt('admin123', gen_salt('bf')), NOW());</code>";
    }
    
} catch (PDOException $e) {
    echo "❌ Error checking admins table: " . $e->getMessage() . "<br>";
    echo "<h3>Create admins table:</h3>";
    echo "<p>Run this SQL in your Supabase SQL Editor:</p>";
    echo "<code>CREATE TABLE admins (id SERIAL PRIMARY KEY, username VARCHAR(50) UNIQUE, password VARCHAR(255), created_at TIMESTAMP DEFAULT NOW());</code>";
}

echo "<h2>Testing Authentication Function</h2>";

// Test the authenticateUser function
if (isset($_POST['test_login'])) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    echo "<h3>Testing login for: $username</h3>";
    
    $result = authenticateUser($username, $password);
    if ($result) {
        echo "✅ Authentication successful!<br>";
        echo "Admin ID: {$result['id']}<br>";
        echo "Username: {$result['username']}<br>";
        echo "Created: {$result['created_at']}<br>";
    } else {
        echo "❌ Authentication failed. Check username and password.<br>";
    }
}

?>

<h2>Test Login Form</h2>
<form method="POST" style="background: #f5f5f5; padding: 20px; border-radius: 5px; max-width: 400px;">
    <div style="margin-bottom: 15px;">
        <label>Username:</label><br>
        <input type="text" name="username" value="admin" style="width: 100%; padding: 8px;">
    </div>
    <div style="margin-bottom: 15px;">
        <label>Password:</label><br>
        <input type="password" name="password" value="admin123" style="width: 100%; padding: 8px;">
    </div>
    <button type="submit" name="test_login" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px;">
        Test Login
    </button>
</form>

<h2>Instructions</h2>
<div style="background: #e3f2fd; padding: 15px; border-left: 4px solid #2196f3; margin: 20px 0;">
    <h3>If you need to create an admin account:</h3>
    <ol>
        <li>Go to your Supabase dashboard</li>
        <li>Open the SQL Editor</li>
        <li>Run this command to create the admins table (if it doesn't exist):
            <br><code>CREATE TABLE admins (id SERIAL PRIMARY KEY, username VARCHAR(50) UNIQUE, password VARCHAR(255), created_at TIMESTAMP DEFAULT NOW());</code>
        </li>
        <li>Run this command to add an admin user:
            <br><code>INSERT INTO admins (username, password) VALUES ('admin', crypt('admin123', gen_salt('bf')));</code>
        </li>
    </ol>
</div>

<div style="background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;">
    <h3>Once admin login works:</h3>
    <p>You can test the actual login page at: <a href="login.php">login.php</a></p>
    <p>Use the same credentials you test here.</p>
</div>
