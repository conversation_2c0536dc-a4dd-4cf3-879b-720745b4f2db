<?php
/**
 * Quick test to verify the agent education function fix
 */

require_once 'config.php';

echo "<h1>Testing Agent Education Function Fix</h1>";

try {
    // Test the function that was causing the error
    echo "<h2>Testing getAgentEducation() function</h2>";
    
    // First, let's see if we have any agents
    $agents = getAllAgents();
    echo "Found " . count($agents) . " agents in the database.<br>";
    
    if (count($agents) > 0) {
        $testAgent = $agents[0];
        $agentId = $testAgent['id'];
        $agentNumber = $testAgent['agent_number'] ?? 'N/A';
        
        echo "Testing with Agent ID: $agentId (Agent Number: $agentNumber)<br>";
        
        // This is the function call that was failing
        $education = getAgentEducation($agentId);
        
        echo "✅ SUCCESS! getAgentEducation() executed without errors.<br>";
        echo "Found " . count($education) . " education records for this agent.<br>";
        
        if (count($education) > 0) {
            echo "<h3>Education Records:</h3>";
            echo "<ul>";
            foreach ($education as $edu) {
                echo "<li>";
                echo "Institution: " . ($edu['institution'] ?? 'N/A') . ", ";
                echo "Qualification: " . ($edu['qualification'] ?? 'N/A') . ", ";
                echo "Year: " . ($edu['year_completed'] ?? 'N/A');
                echo "</li>";
            }
            echo "</ul>";
        } else {
            echo "No education records found for this agent.<br>";
        }
        
    } else {
        echo "⚠️ No agents found in the database. You may need to add some test data.<br>";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "<br>";
    echo "The fix did not work. Please check the SQL query in config.php.<br>";
}

echo "<h2>Database Schema Verification</h2>";

try {
    // Check agents table structure
    $stmt = $pdo->query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'agents' ORDER BY ordinal_position");
    $columns = $stmt->fetchAll();
    
    echo "<h3>Agents Table Structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Column Name</th><th>Data Type</th></tr>";
    foreach ($columns as $column) {
        echo "<tr><td>{$column['column_name']}</td><td>{$column['data_type']}</td></tr>";
    }
    echo "</table>";
    
    // Check if we have the expected columns
    $columnNames = array_column($columns, 'column_name');
    $hasId = in_array('id', $columnNames);
    $hasAgentNumber = in_array('agent_number', $columnNames);
    $hasOldAgentId = in_array('agent_id', $columnNames);
    
    echo "<h3>Schema Status:</h3>";
    echo "<ul>";
    echo "<li>Has 'id' column: " . ($hasId ? "✅ Yes" : "❌ No") . "</li>";
    echo "<li>Has 'agent_number' column: " . ($hasAgentNumber ? "✅ Yes" : "❌ No") . "</li>";
    echo "<li>Has old 'agent_id' column: " . ($hasOldAgentId ? "⚠️ Yes (should be migrated)" : "✅ No (good)") . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "❌ Error checking schema: " . $e->getMessage();
}

echo "<h2>✅ Test Complete</h2>";
echo "<p>If you see this message without errors, the fix was successful!</p>";
?>
