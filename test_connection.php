<?php
/**
 * Test database connection and DNS resolution
 */

echo "<h1>Database Connection Diagnostics</h1>";

// Check current config
echo "<h2>1. Current Database Configuration</h2>";
require_once 'config.php';

// Try to extract connection details (be careful not to expose sensitive info)
echo "Checking connection configuration...<br>";

// Test DNS resolution
echo "<h2>2. DNS Resolution Test</h2>";
$hostname = 'db.urfzjhcoitebjjqjavdp.supabase.co';
echo "Testing DNS resolution for: $hostname<br>";

$ip = gethostbyname($hostname);
if ($ip === $hostname) {
    echo "❌ DNS resolution failed - hostname could not be resolved<br>";
    echo "This means your server cannot reach Supabase servers<br>";
} else {
    echo "✅ DNS resolved to: $ip<br>";
}

// Test basic connectivity
echo "<h2>3. Network Connectivity Test</h2>";
$port = 5432; // PostgreSQL default port

echo "Testing connection to $hostname:$port...<br>";

$connection = @fsockopen($hostname, $port, $errno, $errstr, 10);
if (!$connection) {
    echo "❌ Cannot connect to $hostname:$port<br>";
    echo "Error: $errstr ($errno)<br>";
} else {
    echo "✅ Network connection successful<br>";
    fclose($connection);
}

// Test PDO connection
echo "<h2>4. PDO Connection Test</h2>";
try {
    // Test if $pdo is already connected
    if (isset($pdo)) {
        $stmt = $pdo->query("SELECT 1");
        echo "✅ PDO connection is working<br>";
    } else {
        echo "❌ PDO connection not established<br>";
    }
} catch (Exception $e) {
    echo "❌ PDO connection failed: " . $e->getMessage() . "<br>";
}

// Check server environment
echo "<h2>5. Server Environment</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "OS: " . php_uname() . "<br>";

// Check if we're running locally
$isLocal = in_array($_SERVER['SERVER_NAME'] ?? '', ['localhost', '127.0.0.1']) || 
           strpos($_SERVER['SERVER_NAME'] ?? '', 'localhost') !== false;
echo "Running locally: " . ($isLocal ? "Yes (XAMPP/Local)" : "No (Production)") . "<br>";

// Check internet connectivity
echo "<h2>6. Internet Connectivity Test</h2>";
$testSites = ['google.com', 'github.com', 'supabase.com'];

foreach ($testSites as $site) {
    $ip = gethostbyname($site);
    if ($ip === $site) {
        echo "❌ Cannot resolve $site<br>";
    } else {
        echo "✅ $site resolves to $ip<br>";
    }
}

// Suggestions
echo "<h2>7. Troubleshooting Suggestions</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";

if ($isLocal) {
    echo "<h3>For Local Development (XAMPP):</h3>";
    echo "<ol>";
    echo "<li><strong>Check Internet Connection:</strong> Make sure your computer has internet access</li>";
    echo "<li><strong>Firewall/Antivirus:</strong> Temporarily disable firewall/antivirus to test</li>";
    echo "<li><strong>DNS Settings:</strong> Try changing DNS to ******* or *******</li>";
    echo "<li><strong>XAMPP Configuration:</strong> Restart Apache and check if PHP has internet access</li>";
    echo "<li><strong>Alternative:</strong> Use local PostgreSQL database for development</li>";
    echo "</ol>";
} else {
    echo "<h3>For Production Server:</h3>";
    echo "<ol>";
    echo "<li><strong>Server Firewall:</strong> Check if outbound connections on port 5432 are allowed</li>";
    echo "<li><strong>DNS Configuration:</strong> Contact hosting provider about DNS resolution</li>";
    echo "<li><strong>Network Policies:</strong> Some hosting providers block external database connections</li>";
    echo "</ol>";
}

echo "<h3>Quick Fixes to Try:</h3>";
echo "<ul>";
echo "<li><strong>Restart XAMPP:</strong> Stop and start Apache/MySQL services</li>";
echo "<li><strong>Flush DNS:</strong> Run 'ipconfig /flushdns' in Command Prompt (Windows)</li>";
echo "<li><strong>Check Supabase Status:</strong> Visit <a href='https://status.supabase.com' target='_blank'>status.supabase.com</a></li>";
echo "<li><strong>Try Different Network:</strong> Test with mobile hotspot or different WiFi</li>";
echo "</ul>";

echo "</div>";

?>
