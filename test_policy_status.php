<?php
/**
 * Test Policy Status Management
 */

require_once 'config.php';

echo "<h1>Policy Status Management Test</h1>";

// Check if user is admin (simulate admin session for testing)
session_start();
if (!isset($_SESSION['role'])) {
    $_SESSION['role'] = 'admin'; // Simulate admin for testing
    $_SESSION['username'] = 'test_admin';
    echo "<p><strong>Note:</strong> Simulating admin session for testing</p>";
}

echo "<h2>1. Current Policies</h2>";

try {
    $stmt = $pdo->query("SELECT policy_id, client_id, status, plan_type, created_at FROM policies ORDER BY created_at DESC LIMIT 10");
    $policies = $stmt->fetchAll();
    
    if (count($policies) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th style='padding: 8px;'>Policy ID</th>";
        echo "<th style='padding: 8px;'>Client ID</th>";
        echo "<th style='padding: 8px;'>Plan Type</th>";
        echo "<th style='padding: 8px;'>Current Status</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "<th style='padding: 8px;'>Actions</th>";
        echo "</tr>";
        
        foreach ($policies as $policy) {
            $statusClass = 'status-' . strtolower($policy['status']);
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($policy['policy_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($policy['client_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($policy['plan_type']) . "</td>";
            echo "<td style='padding: 8px;'>";
            echo "<span class='$statusClass' style='padding: 4px 8px; border-radius: 12px; font-size: 12px;'>";
            echo htmlspecialchars($policy['status']);
            echo "</span>";
            echo "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($policy['created_at']) . "</td>";
            echo "<td style='padding: 8px;'>";
            
            // Show available actions based on current status
            $statuses = ['Active', 'Inactive', 'Pending', 'Cancelled'];
            foreach ($statuses as $status) {
                if ($status !== $policy['status']) {
                    echo "<button onclick=\"updateStatus('{$policy['policy_id']}', '$status')\" ";
                    echo "style='margin: 2px; padding: 2px 6px; font-size: 10px;'>";
                    echo "→ $status</button>";
                }
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>2. Test Status Update</h2>";
        echo "<p>Click the buttons above to test status updates, or use the form below:</p>";
        
        echo "<form onsubmit='testStatusUpdate(event)' style='margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo "<label>Policy ID: <select name='policy_id' required>";
        foreach ($policies as $policy) {
            echo "<option value='{$policy['policy_id']}'>{$policy['policy_id']} ({$policy['status']})</option>";
        }
        echo "</select></label><br><br>";
        
        echo "<label>New Status: <select name='status' required>";
        foreach (['Active', 'Inactive', 'Pending', 'Expired', 'Cancelled'] as $status) {
            echo "<option value='$status'>$status</option>";
        }
        echo "</select></label><br><br>";
        
        echo "<button type='submit'>Update Status</button>";
        echo "</form>";
        
    } else {
        echo "<p>No policies found in database.</p>";
        echo "<p><a href='policy_add.php'>Add a test policy</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<h2>3. Test Results</h2>";
echo "<div id='results' style='margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9;'>";
echo "Test results will appear here...";
echo "</div>";

?>

<style>
.status-active { background: #e8f5e9; color: #2e7d32; }
.status-inactive { background: #ffebee; color: #c62828; }
.status-pending { background: #fff3e0; color: #ef6c00; }
.status-cancelled { background: #fafafa; color: #616161; }
.status-expired { background: #eceff1; color: #455a64; }
</style>

<script>
function updateStatus(policyId, newStatus) {
    const resultsDiv = document.getElementById('results');
    resultsDiv.innerHTML = `<p>Updating policy ${policyId} to ${newStatus}...</p>`;
    
    fetch('update_policy_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            policy_id: policyId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultsDiv.innerHTML = `
                <p style="color: green;">✅ SUCCESS: ${data.message}</p>
                <p><strong>Details:</strong></p>
                <ul>
                    <li>Policy ID: ${data.data.policy_id}</li>
                    <li>Old Status: ${data.data.old_status}</li>
                    <li>New Status: ${data.data.new_status}</li>
                    <li>Updated At: ${data.data.updated_at}</li>
                </ul>
                <p><button onclick="location.reload()">Refresh Page</button></p>
            `;
        } else {
            resultsDiv.innerHTML = `<p style="color: red;">❌ ERROR: ${data.message}</p>`;
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = `<p style="color: red;">❌ NETWORK ERROR: ${error.message}</p>`;
    });
}

function testStatusUpdate(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const policyId = formData.get('policy_id');
    const status = formData.get('status');
    updateStatus(policyId, status);
}
</script>

<p><a href="client_view.php?id=<?php echo urlencode($policies[0]['client_id'] ?? 'test'); ?>">← Back to Client View</a></p>
