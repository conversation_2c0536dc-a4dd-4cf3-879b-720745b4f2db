<?php
// test_search.php - A diagnostic tool for the search functionality
session_start();
require_once 'config.php';

// Ensure user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Please log in first.";
    exit;
}

// Get database connection info
$dbInfo = [
    'pdo_connected' => $pdo ? 'Yes' : 'No',
    'driver' => $pdo ? $pdo->getAttribute(PDO::ATTR_DRIVER_NAME) : 'N/A',
    'server_version' => $pdo ? $pdo->getAttribute(PDO::ATTR_SERVER_VERSION) : 'N/A',
];

// Get schema info
$tables = [];
try {
    $stmt = $pdo->query("SHOW TABLES");
    foreach ($stmt as $row) {
        $tableName = $row[0];
        $tables[$tableName] = [
            'name' => $tableName,
            'columns' => [],
            'rows' => 0
        ];
        
        // Get columns
        $columnsStmt = $pdo->query("DESCRIBE $tableName");
        foreach ($columnsStmt as $colRow) {
            $tables[$tableName]['columns'][] = [
                'name' => $colRow['Field'],
                'type' => $colRow['Type']
            ];
        }
        
        // Get row count
        $countStmt = $pdo->query("SELECT COUNT(*) FROM $tableName");
        $tables[$tableName]['rows'] = $countStmt->fetchColumn();
    }
} catch (Exception $e) {
    $tables['error'] = $e->getMessage();
}

// Test actual search query
$sampleQueries = ['a', 'agent', 'client']; // Add more test queries if needed
$searchResults = [];

foreach ($sampleQueries as $query) {
    $searchResults[$query] = [];
    
    try {
        // Test client search
        $clientSql = "SELECT client_id, name, email, phone_number FROM clients 
            WHERE name LIKE :query OR email LIKE :query OR phone_number LIKE :query LIMIT 5";
        $stmt = $pdo->prepare($clientSql);
        $stmt->execute([':query' => "%$query%"]);
        $searchResults[$query]['clients'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Test agent search
        $agentSql = "SELECT agent_id, name, email, phone_number FROM agents 
            WHERE name LIKE :query OR email LIKE :query OR phone_number LIKE :query LIMIT 5";
        $stmt = $pdo->prepare($agentSql);
        $stmt->execute([':query' => "%$query%"]);
        $searchResults[$query]['agents'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $searchResults[$query]['error'] = $e->getMessage();
    }
}

// Output the diagnostic information
header('Content-Type: text/html');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Search Diagnostics</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }
        h1, h2, h3 { color: #333; }
        .section { background: white; padding: 15px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); }
        table { border-collapse: collapse; width: 100%; }
        th, td { text-align: left; padding: 8px; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        tr:hover { background-color: #f5f5f5; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow: auto; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Search Functionality Diagnostics</h1>
    
    <div class="section">
        <h2>Database Connection</h2>
        <table>
            <tr>
                <th>Connected</th>
                <td><?php echo $dbInfo['pdo_connected']; ?></td>
            </tr>
            <tr>
                <th>Driver</th>
                <td><?php echo $dbInfo['driver']; ?></td>
            </tr>
            <tr>
                <th>Server Version</th>
                <td><?php echo $dbInfo['server_version']; ?></td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>Database Schema</h2>
        <p>Tables in the database with their columns and row counts:</p>
        
        <?php foreach ($tables as $table): ?>
            <?php if (isset($table['name'])): ?>
                <h3><?php echo htmlspecialchars($table['name']); ?> (<?php echo $table['rows']; ?> rows)</h3>
                <table>
                    <tr>
                        <th>Column</th>
                        <th>Type</th>
                    </tr>
                    <?php foreach ($table['columns'] as $column): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($column['name']); ?></td>
                            <td><?php echo htmlspecialchars($column['type']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </table>
            <?php else: ?>
                <p class="error">Error: <?php echo htmlspecialchars($table['error']); ?></p>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
    
    <div class="section">
        <h2>Sample Search Results</h2>
        
        <?php foreach ($searchResults as $query => $results): ?>
            <h3>Query: "<?php echo htmlspecialchars($query); ?>"</h3>
            
            <?php if (isset($results['error'])): ?>
                <p class="error">Error: <?php echo htmlspecialchars($results['error']); ?></p>
            <?php else: ?>
                <h4>Clients (<?php echo count($results['clients']); ?>)</h4>
                <?php if (empty($results['clients'])): ?>
                    <p>No client results found.</p>
                <?php else: ?>
                    <table>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                        </tr>
                        <?php foreach ($results['clients'] as $client): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($client['client_id']); ?></td>
                                <td><?php echo htmlspecialchars($client['name']); ?></td>
                                <td><?php echo htmlspecialchars($client['email']); ?></td>
                                <td><?php echo htmlspecialchars($client['phone_number']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>
                
                <h4>Agents (<?php echo count($results['agents']); ?>)</h4>
                <?php if (empty($results['agents'])): ?>
                    <p>No agent results found.</p>
                <?php else: ?>
                    <table>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                        </tr>
                        <?php foreach ($results['agents'] as $agent): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($agent['agent_id']); ?></td>
                                <td><?php echo htmlspecialchars($agent['name']); ?></td>
                                <td><?php echo htmlspecialchars($agent['email']); ?></td>
                                <td><?php echo htmlspecialchars($agent['phone_number']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>
            <?php endif; ?>
            <hr>
        <?php endforeach; ?>
    </div>

    <div class="section">
        <h2>Live Search Test</h2>
        <p>Try the search functionality directly:</p>
        <input type="text" id="test-search" placeholder="Type to search..." style="padding: 8px; width: 300px; margin-bottom: 10px;">
        <div id="test-results" style="border: 1px solid #ddd; padding: 10px; min-height: 100px; background: white;"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(function() {
        var searchTimeout;
        var $testSearch = $('#test-search');
        var $testResults = $('#test-results');
        
        $testSearch.on('input', function() {
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            var query = $(this).val().trim();
            if (query === '') {
                $testResults.html('');
                return;
            }
            
            searchTimeout = setTimeout(function() {
                $testResults.html('Searching...');
                
                $.ajax({
                    url: 'live_search.php',
                    type: 'GET',
                    data: { query: query },
                    dataType: 'json',
                    success: function(data) {
                        var resultsHtml = '<h4>Results:</h4>';
                        
                        if (data.error) {
                            resultsHtml += '<p class="error">Error: ' + data.error + '</p>';
                        } else if (data.length === 0) {
                            resultsHtml += '<p>No results found.</p>';
                        } else {
                            resultsHtml += '<ul>';
                            $.each(data, function(i, item) {
                                resultsHtml += '<li>' + 
                                    item.type + ': ' + 
                                    item.name + ' (' + 
                                    item.id + ') - ' + 
                                    item.email + '</li>';
                            });
                            resultsHtml += '</ul>';
                        }
                        
                        $testResults.html(resultsHtml);
                    },
                    error: function(xhr, status, error) {
                        $testResults.html('<p class="error">AJAX Error: ' + status + ' - ' + error + '</p>' +
                            '<p>Response text: ' + xhr.responseText + '</p>');
                    }
                });
            }, 300);
        });
    });
    </script>
</body>
</html> 