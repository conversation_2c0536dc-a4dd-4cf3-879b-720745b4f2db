# Supabase PostgreSQL Migration Guide

This guide will help you migrate your CRM system from MySQL to Supabase PostgreSQL.

## Prerequisites

1. **Supabase Account**: Create a free account at [supabase.com](https://supabase.com)
2. **PHP PostgreSQL Extension**: Ensure `pdo_pgsql` is installed
3. **Your SQL Dump Files**: Located in the `sql dump/` folder

## Step 1: Set Up Supabase Project

1. Create a new project in Supabase
2. Go to **Settings** → **Database**
3. Note down your connection details:
   - Host: `db.your-project-ref.supabase.co`
   - Database: `postgres`
   - Username: `postgres`
   - Password: (the one you set during project creation)
   - Port: `5432`

## Step 2: Update Configuration

1. Open `config.php`
2. Replace the placeholder values with your actual Supabase credentials:

```php
$supabase_host = "db.your-project-ref.supabase.co";  // Your actual host
$supabase_password = "your-actual-password";         // Your actual password
```

## Step 3: Create Database Schema

Since your SQL dump files contain data but not the schema, you'll need to create the tables first. Go to your Supabase SQL Editor and run:

```sql
-- Create users table
CREATE TABLE users (
    user_id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create agents table
CREATE TABLE agents (
    agent_id VARCHAR(11) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    ic_number VARCHAR(20) NOT NULL,
    gender VARCHAR(10),
    date_of_birth DATE,
    phone_number VARCHAR(20),
    address TEXT,
    beneficiary_phone VARCHAR(20),
    work_experience TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'Pending',
    user_id UUID,
    photo VARCHAR(255)
);

-- Create clients table
CREATE TABLE clients (
    client_id VARCHAR(50) PRIMARY KEY,
    policy_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    name VARCHAR(255),
    ic_number VARCHAR(20),
    client_number VARCHAR(50),
    gender VARCHAR(10),
    date_of_birth DATE,
    phone_number VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    marital_status VARCHAR(20),
    race VARCHAR(50),
    religion VARCHAR(50),
    nationality VARCHAR(50),
    occupation VARCHAR(100),
    exact_duties TEXT,
    nature_of_business TEXT,
    salary_yearly VARCHAR(20),
    company_name VARCHAR(255),
    company_address TEXT,
    weight VARCHAR(10),
    height VARCHAR(10),
    smoker VARCHAR(20),
    hospital_admission_history TEXT,
    status VARCHAR(20)
);

-- Create policies table
CREATE TABLE policies (
    policy_id VARCHAR(50) PRIMARY KEY,
    client_id VARCHAR(50),
    agent_id VARCHAR(11),
    plan_type VARCHAR(100),
    basic_plan_rider VARCHAR(100),
    sum_covered VARCHAR(50),
    coverage_term VARCHAR(10),
    contribution VARCHAR(50),
    start_date DATE,
    end_date DATE,
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    policy_name VARCHAR(255),
    FOREIGN KEY (client_id) REFERENCES clients(client_id),
    FOREIGN KEY (agent_id) REFERENCES agents(agent_id)
);

-- Create educationdetails table
CREATE TABLE educationdetails (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(11),
    level VARCHAR(100),
    year_completed VARCHAR(4),
    institution_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (agent_id) REFERENCES agents(agent_id)
);

-- Create documents table
CREATE TABLE documents (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(11),
    client_id VARCHAR(50),
    document_type VARCHAR(100),
    file_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create activity_logs table
CREATE TABLE activity_logs (
    log_id SERIAL PRIMARY KEY,
    user_id INTEGER,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    timestamp TIMESTAMP DEFAULT NOW()
);

-- Create beneficiaries table (if needed)
CREATE TABLE beneficiaries (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(11),
    name VARCHAR(255),
    relationship VARCHAR(50),
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (agent_id) REFERENCES agents(agent_id)
);

-- Create photos table (if needed)
CREATE TABLE photos (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(11),
    client_id VARCHAR(50),
    photo_path VARCHAR(500),
    photo_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## Step 4: Test Connection

1. Run the test script: `http://your-domain/test_supabase_connection.php`
2. Check for green checkmarks (✅) indicating successful operations
3. Fix any connection issues before proceeding

## Step 5: Import Data

### Option A: Automatic Import (Recommended)
1. Run: `http://your-domain/import_data_from_dump.php`
2. This will attempt to automatically parse and import your SQL dump files

### Option B: Manual Import
1. Open each file in the `sql dump/` folder
2. Copy the INSERT statements
3. In Supabase SQL Editor, modify the statements:
   - Remove `"public".` prefix
   - Remove double quotes around table names
4. Execute each statement

Example transformation:
```sql
-- From:
INSERT INTO "public"."agents" ("agent_id", "name", ...) VALUES ('AG001', 'John Doe', ...);

-- To:
INSERT INTO agents (agent_id, name, ...) VALUES ('AG001', 'John Doe', ...);
```

## Step 6: Update Application Files

Check other PHP files that might still use MySQL-specific functions:
- `agents.php`
- `clients.php` 
- `policies.php`
- Any other files that include `config.php`

## Step 7: Test Your Application

1. Test login functionality
2. Test agent management (add, edit, delete)
3. Test client management
4. Test policy management
5. Verify all CRUD operations work correctly

## Troubleshooting

### Connection Issues
- Verify your Supabase credentials
- Check that `pdo_pgsql` extension is installed: `php -m | grep pgsql`
- Ensure SSL is enabled in your connection

### Data Import Issues
- Check for data type mismatches
- Verify foreign key constraints
- Look for special characters that need escaping

### Application Issues
- Check error logs for MySQL-specific function calls
- Update any remaining `mysqli_*` functions to PDO
- Verify column names match between old and new schema

## Benefits of Migration

✅ **Scalability**: Supabase handles scaling automatically
✅ **Real-time**: Built-in real-time subscriptions
✅ **Security**: Row Level Security (RLS) policies
✅ **API**: Auto-generated REST and GraphQL APIs
✅ **Dashboard**: Built-in admin dashboard
✅ **Backup**: Automated backups and point-in-time recovery

## Support

If you encounter issues:
1. Check the Supabase documentation: [supabase.com/docs](https://supabase.com/docs)
2. Review the error logs in your PHP application
3. Test individual functions using the test scripts provided

## Files Created During Migration

- `config.php` - Updated with PostgreSQL connection
- `config_mysql_backup.php` - Backup of original MySQL config
- `config_supabase.php` - Standalone Supabase config
- `test_supabase_connection.php` - Connection test script
- `import_data_from_dump.php` - Data import script
- `SUPABASE_MIGRATION_GUIDE.md` - This guide
