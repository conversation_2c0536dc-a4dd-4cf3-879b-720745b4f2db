<?php
/**
 * Debug the exact issue with agent_view.php
 */

session_start();
require_once 'config.php';

echo "<h1>Debugging Agent View Issue</h1>";

// Simulate the exact same conditions as agent_view.php
if (!isset($_GET['id'])) {
    echo "❌ No agent ID provided. Add ?id=4 to the URL<br>";
    echo "<a href='debug_agent_view.php?id=4'>Try with ID 4</a><br>";
    exit;
}

$agentId = $_GET['id'];
echo "<h2>1. Testing with Agent ID: $agentId</h2>";

// Test getAgentById first (this works in agent_view.php)
echo "<h3>Step 1: Testing getAgentById($agentId)</h3>";
try {
    $agent = getAgentById($agentId);
    if ($agent) {
        echo "✅ getAgentById() works - Found agent: " . $agent['name'] . "<br>";
        echo "Agent data: " . json_encode($agent) . "<br>";
    } else {
        echo "❌ getAgentById() returned null - Agent not found<br>";
        exit;
    }
} catch (Exception $e) {
    echo "❌ getAgentById() failed: " . $e->getMessage() . "<br>";
    exit;
}

// Test the problematic function
echo "<h3>Step 2: Testing getAgentEducation($agentId)</h3>";
try {
    echo "About to call getAgentEducation($agentId)...<br>";
    
    // Let's manually check what the function will do
    echo "<h4>Manual SQL Test:</h4>";
    $stmt = $pdo->prepare("SELECT ed.* FROM educationdetails ed
                          JOIN agents a ON ed.agent_id = a.id
                          WHERE a.id = ?
                          ORDER BY ed.year_completed DESC");
    echo "SQL prepared successfully<br>";
    
    echo "Executing with agentId: $agentId<br>";
    $stmt->execute([$agentId]);
    echo "✅ Manual SQL execution successful<br>";
    
    $results = $stmt->fetchAll();
    echo "Found " . count($results) . " education records<br>";
    
    // Now try the actual function
    echo "<h4>Function Call Test:</h4>";
    $education = getAgentEducation($agentId);
    echo "✅ getAgentEducation() executed successfully<br>";
    echo "Found " . count($education) . " education records<br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}

// Test database connection
echo "<h2>2. Database Connection Test</h2>";
try {
    $stmt = $pdo->query("SELECT version()");
    $version = $stmt->fetchColumn();
    echo "✅ Database connected: $version<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Check table structure
echo "<h2>3. Table Structure Check</h2>";
try {
    $stmt = $pdo->query("SELECT column_name FROM information_schema.columns WHERE table_name = 'agents' ORDER BY ordinal_position");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Agents table columns: " . implode(', ', $columns) . "<br>";
    
    $stmt = $pdo->query("SELECT column_name FROM information_schema.columns WHERE table_name = 'educationdetails' ORDER BY ordinal_position");
    $eduColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Education table columns: " . implode(', ', $eduColumns) . "<br>";
    
} catch (Exception $e) {
    echo "❌ Error checking tables: " . $e->getMessage() . "<br>";
}

// Check if agent exists
echo "<h2>4. Agent Existence Check</h2>";
try {
    $stmt = $pdo->prepare("SELECT id, name FROM agents WHERE id = ?");
    $stmt->execute([$agentId]);
    $agentCheck = $stmt->fetch();
    
    if ($agentCheck) {
        echo "✅ Agent $agentId exists: " . $agentCheck['name'] . "<br>";
    } else {
        echo "❌ Agent $agentId does not exist<br>";
        
        // Show available agents
        $stmt = $pdo->query("SELECT id, name FROM agents LIMIT 5");
        $agents = $stmt->fetchAll();
        echo "Available agents:<br>";
        foreach ($agents as $a) {
            echo "- ID: {$a['id']}, Name: {$a['name']} <a href='debug_agent_view.php?id={$a['id']}'>[Test]</a><br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking agent: " . $e->getMessage() . "<br>";
}

echo "<h2>5. Function Source Check</h2>";
$reflection = new ReflectionFunction('getAgentEducation');
$filename = $reflection->getFileName();
$startLine = $reflection->getStartLine();
echo "Function defined in: $filename at line $startLine<br>";

?>
