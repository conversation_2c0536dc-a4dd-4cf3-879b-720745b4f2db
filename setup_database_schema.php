<?php
// setup_database_schema.php - Create database schema for Supabase

echo "<h1>Supabase Database Schema Setup</h1>";

require_once 'config.php';

// Function to execute SQL and report results
function executeSQL($pdo, $sql, $description) {
    try {
        $pdo->exec($sql);
        echo "✅ $description<br>";
        return true;
    } catch (PDOException $e) {
        echo "❌ $description - Error: " . $e->getMessage() . "<br>";
        return false;
    }
}

echo "<h2>Creating Database Tables</h2>";

// Create users table (FlutterFlow structure)
$sql = "CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP DEFAULT NOW(),
    email VARCHAR(255) UNIQUE NOT NULL
)";
executeSQL($pdo, $sql, "Created users table");

// Create agents table
$sql = "CREATE TABLE IF NOT EXISTS agents (
    agent_id VARCHAR(50) PRIMARY KEY,
    name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
    ic_number VARCHAR(20),
    gender VARCHAR(20),
    date_of_birth DATE,
    phone_number VARCHAR(20),
    address TEXT,
    beneficiary_phone VARCHAR(20),
    work_experience TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    status VARCHAR(20),
    user_id UUID,
    photo VARCHAR(255)
)";
executeSQL($pdo, $sql, "Created agents table");

// Create clients table
$sql = "CREATE TABLE IF NOT EXISTS clients (
    client_id VARCHAR(50) PRIMARY KEY,
    policy_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    name VARCHAR(255),
    ic_number VARCHAR(20),
    client_number VARCHAR(50),
    gender VARCHAR(20),
    date_of_birth DATE,
    phone_number VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    marital_status VARCHAR(50),
    race VARCHAR(50),
    religion VARCHAR(50),
    nationality VARCHAR(50),
    occupation VARCHAR(100),
    exact_duties TEXT,
    nature_of_business TEXT,
    salary_yearly VARCHAR(20),
    company_name VARCHAR(255),
    company_address TEXT,
    weight VARCHAR(10),
    height VARCHAR(10),
    smoker VARCHAR(20),
    hospital_admission_history TEXT,
    status VARCHAR(20)
)";
executeSQL($pdo, $sql, "Created clients table");

// Create policies table
$sql = "CREATE TABLE IF NOT EXISTS policies (
    policy_id VARCHAR(50) PRIMARY KEY,
    client_id VARCHAR(50),
    agent_id VARCHAR(50),
    plan_type VARCHAR(100),
    basic_plan_rider VARCHAR(100),
    sum_covered VARCHAR(50),
    coverage_term VARCHAR(10),
    contribution VARCHAR(50),
    start_date DATE,
    end_date DATE,
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    policy_name VARCHAR(255)
)";
executeSQL($pdo, $sql, "Created policies table");

// Create educationdetails table
$sql = "CREATE TABLE IF NOT EXISTS educationdetails (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(50),
    level VARCHAR(100),
    year_completed VARCHAR(4),
    institution_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
)";
executeSQL($pdo, $sql, "Created educationdetails table");

// Create documents table
$sql = "CREATE TABLE IF NOT EXISTS documents (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(50),
    client_id VARCHAR(50),
    document_type VARCHAR(100),
    file_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
)";
executeSQL($pdo, $sql, "Created documents table");

// Create activity_logs table
$sql = "CREATE TABLE IF NOT EXISTS activity_logs (
    log_id SERIAL PRIMARY KEY,
    user_id UUID,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    timestamp TIMESTAMP DEFAULT NOW()
)";
executeSQL($pdo, $sql, "Created activity_logs table");

// Create beneficiaries table
$sql = "CREATE TABLE IF NOT EXISTS beneficiaries (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(50),
    name VARCHAR(255),
    relationship VARCHAR(50),
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
)";
executeSQL($pdo, $sql, "Created beneficiaries table");

// Create photos table
$sql = "CREATE TABLE IF NOT EXISTS photos (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(50),
    client_id VARCHAR(50),
    photo_path VARCHAR(500),
    photo_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
)";
executeSQL($pdo, $sql, "Created photos table");

echo "<h2>Adding Foreign Key Constraints</h2>";

// Add foreign key constraints (optional, but good practice)
$constraints = [
    "ALTER TABLE policies ADD CONSTRAINT fk_policies_client FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE",
    "ALTER TABLE policies ADD CONSTRAINT fk_policies_agent FOREIGN KEY (agent_id) REFERENCES agents(agent_id) ON DELETE SET NULL",
    "ALTER TABLE educationdetails ADD CONSTRAINT fk_education_agent FOREIGN KEY (agent_id) REFERENCES agents(agent_id) ON DELETE CASCADE"
];

foreach ($constraints as $constraint) {
    try {
        $pdo->exec($constraint);
        echo "✅ Added foreign key constraint<br>";
    } catch (PDOException $e) {
        // Ignore if constraint already exists
        if (strpos($e->getMessage(), 'already exists') === false) {
            echo "⚠️ Foreign key constraint skipped: " . $e->getMessage() . "<br>";
        }
    }
}

echo "<h2>Creating Default Admin User</h2>";

// Check if admin user exists
$stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'");
$stmt->execute();
$adminExists = $stmt->fetchColumn() > 0;

if (!$adminExists) {
    $sql = "INSERT INTO users (email, created_at) VALUES ('<EMAIL>', NOW())";
    $stmt = $pdo->prepare($sql);
    if ($stmt->execute()) {
        echo "✅ Created default admin user (email: <EMAIL>)<br>";
        echo "ℹ️ <strong>Note:</strong> FlutterFlow handles authentication, so no password is stored here<br>";
    } else {
        echo "❌ Failed to create admin user<br>";
    }
} else {
    echo "ℹ️ Admin user already exists<br>";
}

echo "<h2>Verification</h2>";

// Verify tables were created
$tables = ['users', 'agents', 'clients', 'policies', 'educationdetails', 'documents', 'activity_logs', 'beneficiaries', 'photos'];

foreach ($tables as $table) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "✅ Table '$table' exists with $count records<br>";
    } catch (PDOException $e) {
        echo "❌ Table '$table' verification failed: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>Next Steps</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>✅ Database Schema Setup Complete!</h3>";
echo "<p>Your database tables have been created. Now you can:</p>";
echo "<ol>";
echo "<li><strong>Import your data:</strong> Run <a href='import_data_from_dump.php'>import_data_from_dump.php</a></li>";
echo "<li><strong>Test your application:</strong> Try logging in with username: <code>admin</code>, password: <code>admin123</code></li>";
echo "<li><strong>Test functionality:</strong> Add/edit agents, clients, and policies</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 20px 0;'>";
echo "<h3>⚠️ Security Reminder</h3>";
echo "<p>Don't forget to:</p>";
echo "<ul>";
echo "<li>Change the default admin password</li>";
echo "<li>Set up proper user accounts</li>";
echo "<li>Configure Supabase Row Level Security (RLS) if needed</li>";
echo "</ul>";
echo "</div>";
?>
